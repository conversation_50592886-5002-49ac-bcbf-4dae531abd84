// Use existing global variables if they exist, otherwise create new ones
let csvTable = window.csvTable || null;
let csvData = window.csvData || [];
let csvFiles = window.csvFiles || [];
let currentCsvIndex = window.currentCsvIndex !== undefined ? window.currentCsvIndex : -1;
let csvTemplates = window.csvTemplates || [];

function initCsvManager() {
    if (!document.getElementById('csvTable')) {
        console.error("CSV Table element not found. Delaying initialization...");
        setTimeout(initCsvManager, 100);
        return;
    }
    initializeCsvTable();
    loadTemplates();
    window.csvTable = csvTable;
    window.csvData = csvData;
    window.csvFiles = csvFiles;
    window.csvTemplates = csvTemplates;
    document.getElementById('addRowBtn')?.addEventListener('click', addNewRow);
    document.getElementById('deleteSelectedRowsBtn')?.addEventListener('click', deleteSelectedRows);
    document.getElementById('duplicateSelectedRowsBtn')?.addEventListener('click', duplicateSelectedRows);
    document.getElementById('editSelectedRowsBtn')?.addEventListener('click', editSelectedRows);
    document.getElementById('undoBtn')?.addEventListener('click', () => csvTable.undo());
    document.getElementById('redoBtn')?.addEventListener('click', () => csvTable.redo());
    document.getElementById('csvFileInput')?.addEventListener('change', handleCsvFileSelection);
    document.getElementById('exportCsvBtn')?.addEventListener('click', exportCsvFile);
    document.getElementById('deleteAllCsvBtn')?.addEventListener('click', deleteAllCsvFiles);
    document.getElementById('clearCsvBtn')?.addEventListener('click', clearCsvTable);

    // Ensure the New Table Modal exists in the DOM
    ensureNewTableModalExists();

    // Add event listener for New Table button if it exists
    const newTableButton = document.getElementById('csvNewTableButton');
    if (newTableButton) {
        newTableButton.addEventListener('click', showNewTableModal);
    } else {
        console.warn("New Table Button not found. It will be initialized when available.");
        // Try to add the event listener when the DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            const newTableButtonDelayed = document.getElementById('csvNewTableButton');
            if (newTableButtonDelayed) {
                newTableButtonDelayed.addEventListener('click', showNewTableModal);
                console.log("New Table Button initialized after DOM loaded");
            } else {
                console.error("New Table Button not found even after DOM loaded");
            }
        });
    }

    console.log("CSV Manager initialized successfully");
}

// Function to ensure the New Table Modal exists in the DOM
function ensureNewTableModalExists() {
    // Check if the modal already exists
    if (!document.getElementById('newTableModal')) {
        console.log('New Table Modal not found in DOM, creating it now...');
        createNewTableModal();
    } else {
        console.log('New Table Modal already exists in DOM');
    }
}

function debugCsvManager() {
    console.log("CSV Manager Debug Info:");
    console.log("- csvTable initialized:", csvTable ? "Yes" : "No");
    console.log("- csvFiles count:", csvFiles.length);
    console.log("- currentCsvIndex:", currentCsvIndex);
    console.log("- csvTemplates count:", csvTemplates.length);
    console.log("- DOM Elements:");
    console.log("  - csvTable:", document.getElementById('csvTable') ? "Found" : "Not Found");
    console.log("  - csvFileInput:", document.getElementById('csvFileInput') ? "Found" : "Not Found");
    console.log("  - csvFilesList:", document.getElementById('csvFilesList') ? "Found" : "Not Found");
    console.log("  - exportCsvBtn:", document.getElementById('exportCsvBtn') ? "Found" : "Not Found");
}

window.debugCsvManager = debugCsvManager;
// Using global showToast function from toast-manager.js

window.handleCsvFileSelection = handleCsvFileSelection;
window.exportCsvFile = exportCsvFile;
window.clearCsvTable = clearCsvTable;
window.deleteAllCsvFiles = deleteAllCsvFiles;
window.deleteAllTemplatesStub = deleteAllTemplatesStub;
window.showNewTableModal = showNewTableModal;
window.showExportCsvModal = showExportCsvModal;
window.handleCsvExportConfirm = handleCsvExportConfirm;
window.initCsvManager = initCsvManager;
function initializeCsvTable() {
    csvTable = new Tabulator("#csvTable", {
        data: [],
        layout: "fitColumns",
        pagination: "local",
        paginationSize: 10,
        paginationSizeSelector: [5, 10, 20, 50, true],
        movableColumns: true,
        selectable: "multiple",
        selectableRangeMode: "click",
        placeholder: "No CSV Data Available - Please Import a CSV File",
        columns: [],
        rowContextMenu: csvRowMenu,
        headerContextMenu: headerMenu,
        history: true,
        index: "tabulator_internal_row_id",
        responsiveLayout: false,
        layoutColumnsOnNewData: true,
        autoResize: true,
        resizableColumns: true
    });
    csvTable.on("cellEdited", function (cell) {
        if (currentCsvIndex >= 0) {
            csvFiles[currentCsvIndex].data = csvTable.getData();
        }
    });
}

const csvRowMenu = [
    {
        label: "Edit Selected Rows",
        action: function (e, row) {
            if (!row.isSelected()) {
                row.select();
            }
            editSelectedRows();
        }
    },
    {
        label: "Delete Row",
        action: function (e, row) {
            if (confirm("Are you sure you want to delete this row?")) {
                row.delete();
                showToast("Row deleted successfully");
            }
        }
    },
    {
        label: "Insert Row Above",
        action: function (e, row) {
            const newRow = {};
            const columns = csvTable.getColumns();
            columns.forEach(column => {
                newRow[column.getField()] = "";
            });
            csvTable.addRow(newRow, true, row);
            showToast("Row inserted successfully");
        }
    },
    {
        label: "Insert Row Below",
        action: function (e, row) {
            const newRow = {};
            const columns = csvTable.getColumns();
            columns.forEach(column => {
                newRow[column.getField()] = "";
            });
            csvTable.addRow(newRow, false, row);
            showToast("Row inserted successfully");
        }
    }
];

const headerMenu = [
    {
        label: "Add Column",
        action: function (e, column) {
            const columnName = prompt("Enter column name:");
            if (columnName) {
                csvTable.addColumn({
                    title: columnName,
                    field: columnName,
                    editor: "input",
                    headerFilter: "input"
                }, true, column);
                showToast("Column added successfully");
            }
        }
    },
    {
        label: "Delete Column",
        action: function (e, column) {
            if (confirm("Are you sure you want to delete this column?")) {
                column.delete();
                showToast("Column deleted successfully");
            }
        }
    },
    {
        label: "Rename Column",
        action: function (e, column) {
            const newName = prompt("Enter new column name:", column.getDefinition().title);
            if (newName) {
                column.updateDefinition({ title: newName, field: newName });
                showToast("Column renamed successfully");
            }
        }
    }
];

function handleCsvFileSelection(e) {
    if (e.target.files.length > 0) {
        Array.from(e.target.files).forEach(file => {
            importCsvFile(file);
        });
    }
}
function importCsvFile(file) {
    Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: function (results) {
            if (results.data.length > 0) {
                const csvFile = {
                    name: file.name,
                    data: results.data,
                    columns: results.meta.fields.map(field => ({
                        title: field,
                        field: field,
                        editor: "input",
                        headerFilter: "input"
                    }))
                };
                csvFiles.push(csvFile);
                currentCsvIndex = csvFiles.length - 1;
                updateCsvFilesList();
                displayCsvFile(currentCsvIndex);
                showToast(`CSV file "${file.name}" imported successfully`);
            } else {
                showToast("The CSV file appears to be empty", "error");
            }
        },
        error: function (error) {
            console.error("Error parsing CSV file:", error);
            showToast("Error importing CSV file", "error");
        }
    });
    document.getElementById('csvFileInput').value = '';
}
function updateCsvFilesList() {
    const csvFilesList = document.getElementById('csvFilesList');
    if (csvFiles.length === 0) {
        csvFilesList.innerHTML = '<li class="list-group-item text-center text-muted">No CSV files imported</li>';
        return;
    }
    csvFilesList.innerHTML = '';
    csvFiles.forEach((file, index) => {
        const listItem = document.createElement('li');
        listItem.className = `list-group-item d-flex justify-content-between align-items-center${index === currentCsvIndex ? ' active' : ''}`;
        listItem.addEventListener('click', (e) => {
            if (!e.target.closest('.btn-group')) {
                displayCsvFile(index);
            }
        });
        const fileNameSpan = document.createElement('span');
        fileNameSpan.textContent = file.name;
        const rowCount = document.createElement('span');
        rowCount.className = 'badge bg-primary rounded-pill';
        rowCount.textContent = file.data.length;
        const actionButtons = document.createElement('div');
        actionButtons.className = 'btn-group';
        const viewButton = document.createElement('button');
        viewButton.className = 'btn btn-outline-primary btn-sm bi bi-eye';
        viewButton.title = 'View';
        viewButton.addEventListener('click', (e) => {
            e.stopPropagation();
            displayCsvFile(index);
        });
        const deleteButton = document.createElement('button');
        deleteButton.className = 'btn btn-outline-danger btn-sm bi bi-trash';
        deleteButton.title = 'Delete';
        deleteButton.addEventListener('click', (e) => {
            e.stopPropagation();
            deleteCsvFile(index);
        });
        actionButtons.appendChild(viewButton);
        actionButtons.appendChild(deleteButton);
        listItem.appendChild(fileNameSpan);
        listItem.appendChild(rowCount);
        listItem.appendChild(actionButtons);
        csvFilesList.appendChild(listItem);
    });
}
function displayCsvFile(index) {
    if (index < 0 || index >= csvFiles.length) {
        return;
    }
    currentCsvIndex = index;
    const csvFile = csvFiles[index];
    const columns = [
        {
            formatter: "rownum",
            hozAlign: "center",
            headerSort: false,
            width: 40,
            title: "No.",
            download: false,
            print: false
        },
        {
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            titleFormatterParams: {
                rowRange: "active"
            },
            hozAlign: "center",
            headerSort: false,
            width: 40,
            download: false,
            print: false
        },
        ...csvFile.columns
    ];
    csvTable.setColumns(columns);
    csvTable.setData(csvFile.data);
    document.getElementById('currentCsvTitle').textContent = ` ${csvFile.name}`;
    updateCsvFilesList();
}
function deleteCsvFile(index) {
    if (index < 0 || index >= csvFiles.length) {
        return;
    }
    if (confirm(`Are you sure you want to delete "${csvFiles[index].name}"?`)) {
        csvFiles.splice(index, 1);
        if (currentCsvIndex === index) {
            currentCsvIndex = csvFiles.length - 1;
            if (currentCsvIndex >= 0) {
                displayCsvFile(currentCsvIndex);
            } else {
                csvTable.clearData();
                csvTable.setColumns([]);
                document.getElementById('currentCsvTitle').textContent = ' CSV Table';
            }
        } else if (currentCsvIndex > index) {
            currentCsvIndex--;
        }
        updateCsvFilesList();
        showToast("CSV file deleted successfully");
    }
}
function exportCsvFile() {
    // Check if there's a valid CSV file loaded and it has data
    if (currentCsvIndex < 0 || !csvFiles[currentCsvIndex] || !csvTable || csvTable.getData().length === 0) {
        showToast("No data to export", "error");
        return;
    }

    // Show the export modal
    showExportCsvModal();
}

function showExportCsvModal() {
    // Ensure the modal exists
    let modal = document.getElementById('exportCsvModal');

    if (!modal) {
        console.log("Export modal not found, creating it dynamically");

        // Create the modal dynamically
        const modalHTML = `
        <div class="modal fade" id="exportCsvModal" tabindex="-1" aria-labelledby="exportCsvModalLabel">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary-purple text-white">
                        <h5 class="modal-title" id="exportCsvModalLabel">Export CSV</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="csvFileNameInput" class="form-label">File Name:</label>
                            <input type="text" class="form-control" id="csvFileNameInput" placeholder="Enter file name">
                        </div>
                        <div class="mb-3">
                            <label for="csvExportOptions" class="form-label">Export Options:</label>
                            <select class="form-select" id="csvExportOptions">
                                <option value="all">All</option>
                                <option value="custom">Custom</option>
                            </select>
                        </div>
                        <div id="csvCheckboxContainer" class="mb-3">
                            <div class="form-check mb-2">
                                <input class="form-check-input csvExportCheckbox" type="checkbox" value="Shutterstock" id="checkShutterstock" checked>
                                <label class="form-check-label" for="checkShutterstock">Shutterstock</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input csvExportCheckbox" type="checkbox" value="Adobe Stock" id="checkAdobeStock" checked>
                                <label class="form-check-label" for="checkAdobeStock">Adobe Stock</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input csvExportCheckbox" type="checkbox" value="Canva" id="checkCanva" checked>
                                <label class="form-check-label" for="checkCanva">Canva</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input csvExportCheckbox" type="checkbox" value="Freepik" id="checkFreepik" checked>
                                <label class="form-check-label" for="checkFreepik">Freepik</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input csvExportCheckbox" type="checkbox" value="Vecteezy" id="checkVecteezy" checked>
                                <label class="form-check-label" for="checkVecteezy">Vecteezy</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input csvExportCheckbox" type="checkbox" value="Dreamstime" id="checkDreamstime" checked>
                                <label class="form-check-label" for="checkDreamstime">Dreamstime</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary-purple" id="confirmCsvExport">Export</button>
                    </div>
                </div>
            </div>
        </div>`;

        // Append the modal to the body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get the modal element again
        modal = document.getElementById('exportCsvModal');

        // Add CSS for the modal
        const style = document.createElement('style');
        style.textContent = `
            #exportCsvModal .modal-header {
                background-color: var(--primary-purple);
                color: white;
            }

            #exportCsvModal .modal-body {
                background-color: var(--purple-100);
                padding: 20px;
            }

            #exportCsvModal .form-label {
                color: var(--purple-900);
                font-weight: 500;
            }

            #exportCsvModal .form-control:focus,
            #exportCsvModal .form-select:focus {
                border-color: var(--primary-purple);
                box-shadow: 0 0 0 0.2rem rgba(123, 31, 162, 0.25);
            }

            #exportCsvModal .form-check-input:checked {
                background-color: var(--primary-purple);
                border-color: var(--primary-purple);
            }

            #exportCsvModal .form-check-label {
                color: var(--purple-900);
            }

            #exportCsvModal .modal-footer {
                background-color: var(--purple-200);
                border-top: 1px solid var(--purple-300);
            }

            #exportCsvModal #confirmCsvExport {
                background-color: var(--primary-purple);
                border-color: var(--primary-purple);
            }

            #exportCsvModal #confirmCsvExport:hover {
                background-color: var(--hard-purple);
                border-color: var(--hard-purple);
            }
        `;
        document.head.appendChild(style);
    }

    // Get the current CSV file name without extension (if available)
    let currentFileName = "export";
    if (currentCsvIndex >= 0 && csvFiles[currentCsvIndex] && csvFiles[currentCsvIndex].name) {
        currentFileName = csvFiles[currentCsvIndex].name.replace(/\.[^/.]+$/, "");
    }

    // Set the file name input value
    const fileNameInput = document.getElementById('csvFileNameInput');
    if (fileNameInput) {
        fileNameInput.value = currentFileName;
    }

    // Set up event listeners for the export options
    const exportOptions = document.getElementById('csvExportOptions');
    if (exportOptions) {
        exportOptions.value = 'all';

        // Remove existing event listeners by cloning and replacing
        const newExportOptions = exportOptions.cloneNode(true);
        exportOptions.parentNode.replaceChild(newExportOptions, exportOptions);

        // Add new event listener
        newExportOptions.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.csvExportCheckbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = newExportOptions.value === 'all';
            });
        });
    }

    // Reset all checkboxes to checked
    const checkboxes = document.querySelectorAll('.csvExportCheckbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });

    // Set up event listener for the export button
    const confirmButton = document.getElementById('confirmCsvExport');
    if (confirmButton) {
        // Remove any existing event listeners
        const newConfirmButton = confirmButton.cloneNode(true);
        confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

        // Add new event listener
        newConfirmButton.addEventListener('click', handleCsvExportConfirm);
    }

    // Show the modal
    try {
        // Check if bootstrap is available
        if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
            console.error("Bootstrap Modal is not available");
            showToast("Error: Bootstrap Modal is not available. Please refresh the page and try again.", "error");
            return;
        }

        // Check if the modal already has a Bootstrap Modal instance
        let modalInstance = bootstrap.Modal.getInstance(modal);

        if (!modalInstance) {
            // Create a new modal instance if one doesn't exist
            modalInstance = new bootstrap.Modal(modal);
        }

        // Show the modal
        modalInstance.show();
    } catch (error) {
        console.error("Error showing modal:", error);
        showToast("Error showing export modal. Please try again.", "error");
    }
}

function handleCsvExportConfirm() {
    // Get the file name
    const fileName = document.getElementById('csvFileNameInput').value.trim();
    if (!fileName) {
        showToast("Please enter a file name", "error");
        return;
    }

    // Get the export options
    const exportOptions = document.getElementById('csvExportOptions').value;

    // Get the selected options
    const selectedOptions = Array.from(document.querySelectorAll('.csvExportCheckbox:checked'))
        .map(checkbox => checkbox.value);

    // Get the current data
    const data = csvTable.getData();

    // Update the data in csvFiles if available
    if (currentCsvIndex >= 0 && csvFiles[currentCsvIndex]) {
        csvFiles[currentCsvIndex].data = data;
    }

    // Define header mappings for different platforms
    const headerMap = {
        'Shutterstock': ['Filename', 'Description', 'Keywords', 'Categories', 'Editorial', 'Mature content', 'illustration'],
        'Adobe Stock': ['Filename', 'Title', 'Keywords', 'Category', 'Releases'],
        'Canva': ['filename', 'title', 'keywords', 'Artist', 'locale', 'description'],
        'Freepik': ['Filename', 'Title', 'Keywords', 'Prompt', 'Model'],
        'Vecteezy': ['Filename', 'Title', 'Description', 'Keywords', 'License'],
        'Dreamstime': ['Filename', 'Image Name', 'Description', 'Category 1', 'Category 2', 'Category 3', 'keywords', 'Free', 'W-EL', 'P-EL', 'SR-EL', 'SR-Price', 'Editorial', 'MR doc Ids', 'Pr Docs']
    };

    // Define initials for file naming
    const initialsMap = {
        'Shutterstock': 'SS',
        'Adobe Stock': 'AS',
        'Canva': 'Canva',
        'Freepik': 'Freepik',
        'Vecteezy': 'Vecteezy',
        'Dreamstime': 'DS'
    };

    // If custom option is selected and no checkboxes are checked, export as is
    if (exportOptions === 'custom' && selectedOptions.length === 0) {
        const csv = Papa.unparse(data, {
            delimiter: ",",
            header: true
        });
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${fileName}_Custom.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Hide the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportCsvModal'));
        if (modal) {
            modal.hide();
        }

        showToast(`CSV exported as ${fileName}_Custom.csv`, "success");
        return;
    }

    // Determine which options to export
    const optionsToExport = selectedOptions.length === 0 ? Object.keys(headerMap) : selectedOptions;

    // Get the headers from the current data
    const headers = Object.keys(data[0] || {});

    // Export for each selected option
    optionsToExport.forEach(option => {
        if (headerMap[option]) {
            const exportData = [];

            // Add the header row
            exportData.push(headerMap[option]);

            // Add the data rows
            data.forEach(row => {
                const rowData = [];
                headerMap[option].forEach(header => {
                    // Find the matching header in the current data
                    const matchingHeader = headers.find(h => h.toLowerCase() === header.toLowerCase());
                    const value = matchingHeader ? row[matchingHeader] || '' : '';
                    rowData.push(value);
                });
                exportData.push(rowData);
            });

            // Create the CSV content
            const csv = Papa.unparse(exportData, {
                delimiter: ",",
                header: false
            });

            // Create and download the file
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            const initials = initialsMap[option] || option;
            link.setAttribute('download', `${fileName}_${initials}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    });

    // Hide the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('exportCsvModal'));
    if (modal) {
        modal.hide();
    }

    showToast(`CSV exported successfully for ${optionsToExport.length} platform(s)`, "success");
}
function clearCsvTable() {
    if (currentCsvIndex < 0 || csvTable.getData().length === 0) {
        showToast("Table is already empty", "info");
        return;
    }
    if (confirm("Are you sure you want to clear the table? This will remove all data but keep the columns.")) {
        const emptyData = [];
        csvTable.setData(emptyData);
        csvFiles[currentCsvIndex].data = emptyData;
        updateCsvFilesList();
        showToast("Table cleared successfully");
    }
}
function addNewRow() {
    if (currentCsvIndex < 0) {
        showToast("Please import a CSV file first", "error");
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'addRowModal';
    modal.setAttribute('aria-labelledby', 'addRowModalLabel');
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-modal', 'true');

    const columns = csvTable.getColumns()
        .filter(col => !['rownum', 'rowSelection'].includes(col.getField()))
        .map(col => ({
            field: col.getField(),
            title: col.getDefinition().title || col.getField()
        }));

    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered modal-lg" style="max-width: 97%;">
            <div class="modal-content p-3">
                <div class="modal-header p-3 border-bottom mb-3">
                    <h5 class="modal-title h6 mb-0" id="addRowModalLabel">Add New Row(s)</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0" style="overflow-y: auto;">
                    <form id="addRowForm">
                        <div class="mb-3">
                            <label for="rowCount" class="form-label">Number of Rows</label>
                            <input type="number" class="form-control" id="rowCount" min="1" value="1">
                        </div>
                        <div id="columnsContainer" style="display: flex; gap: 20px; overflow-x: auto;">
                            ${columns.filter(col => !['rownum', 'rowSelection'].includes(col.field)).map(col => `
                                <div class="column-inputs" data-column="${col.field}" style="min-width: 300px;">
                                    <label class="form-label">${col.title}</label>
                                    <div class="input-sets">
                                        <div class="input-set mb-3">
                                            <div class="input-group">
                                                <select class="form-select input-format" style="max-width: 120px;">
                                                    <option value="text">Text</option>
                                                    <option value="number">Number</option>
                                                    <option value="date">Date</option>
                                                </select>
                                                <input type="text" class="form-control value-input" placeholder="Enter value">
                                                <input type="number" class="form-control number-input d-none" placeholder="Start number" min="1" value="1">
                                                <input type="date" class="form-control date-input d-none">
                                                <button type="button" class="btn btn-outline-danger delete-input d-none">×</button>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary add-input-btn w-100 mt-2">Add More Input</button>
                                </div>
                            `).join('')}
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmAddRows">Add Rows</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    // Set up event handlers for each column
    columns.forEach(col => {
        const columnContainer = modal.querySelector(`[data-column="${col.field}"]`);
        const addInputBtn = columnContainer.querySelector('.add-input-btn');
        const inputSets = columnContainer.querySelector('.input-sets');

        // Setup add input button
        addInputBtn.addEventListener('click', () => {
            const newInputSet = document.createElement('div');
            newInputSet.className = 'input-set mb-3';
            newInputSet.innerHTML = `
                <div class="input-group">
                    <select class="form-select input-format" style="max-width: 120px;">
                        <option value="text">Text</option>
                        <option value="number">Number</option>
                        <option value="date">Date</option>
                    </select>
                    <input type="text" class="form-control value-input" placeholder="Enter value">
                    <input type="number" class="form-control number-input d-none" placeholder="Start number" min="1" value="1">
                    <input type="date" class="form-control date-input d-none">
                    <button type="button" class="btn btn-outline-danger delete-input d-none">×</button>
                </div>
            `;

        // Setup format change handler and delete button for the new input set
        setupFormatChangeHandler(newInputSet);

        // Show delete button for new input set since it's not the first one
        const deleteBtn = newInputSet.querySelector('.delete-input');
        deleteBtn.classList.remove('d-none');
        deleteBtn.addEventListener('click', () => {
            newInputSet.remove();
            // Update delete button visibility for remaining inputs
            const allInputSets = inputSets.querySelectorAll('.input-set');
            if (allInputSets.length === 1) {
                allInputSets[0].querySelector('.delete-input').classList.add('d-none');
            }
        });

            inputSets.appendChild(newInputSet);
        });

        // Setup initial input set
        setupFormatChangeHandler(columnContainer.querySelector('.input-set'));

        // Setup initial input set's delete button visibility
        const initialDeleteBtn = columnContainer.querySelector('.delete-input');
        if (initialDeleteBtn) {
            initialDeleteBtn.classList.add('d-none');
        }
    });

    // Function to setup format change handler
    function setupFormatChangeHandler(inputSet) {
        if (!inputSet) return;

        const formatSelect = inputSet.querySelector('.input-format');
        const textInput = inputSet.querySelector('.value-input');
        const numberInput = inputSet.querySelector('.number-input');
        const dateInput = inputSet.querySelector('.date-input');

        formatSelect?.addEventListener('change', () => {
            textInput.classList.add('d-none');
            numberInput.classList.add('d-none');
            dateInput.classList.add('d-none');

            switch(formatSelect.value) {
                case 'text':
                    textInput.classList.remove('d-none');
                    break;
                case 'number':
                    numberInput.classList.remove('d-none');
                    break;
                case 'date':
                    dateInput.classList.remove('d-none');
                    break;
            }
        });

        // Initialize with first format
        if (textInput) {
            textInput.classList.remove('d-none');
        }
    }

    // Handle form submission
    document.getElementById('confirmAddRows').addEventListener('click', () => {
        const rowCount = parseInt(document.getElementById('rowCount').value, 10);
        const newRows = [];

        for (let i = 0; i < rowCount; i++) {
            const newRow = {};
            columns.forEach(col => {
                const container = modal.querySelector(`[data-column="${col.field}"]`);
                const inputSets = container.querySelectorAll('.input-set');
                const values = [];

                inputSets.forEach(inputSet => {
                    const format = inputSet.querySelector('.input-format').value;
                    const textInput = inputSet.querySelector('.value-input');
                    const numberInput = inputSet.querySelector('.number-input');
                    const dateInput = inputSet.querySelector('.date-input');

                    let value = '';
                    switch(format) {
                        case 'text':
                            value = textInput.value;
                            break;
                        case 'number':
                            const startNum = parseInt(numberInput.value, 10) || 0;
                            value = (startNum + i).toString().padStart(2, '0');
                            break;
                        case 'date':
                            if (dateInput.value) {
                                const date = new Date(dateInput.value);
                                date.setDate(date.getDate() + i);
                                value = date.toISOString().split('T')[0];
                            }
                            break;
                    }
                    if (value) values.push(value);
                });

                newRow[col.field] = values.join(', ');
            });
            newRows.push(newRow);
        }

        csvTable.addData(newRows);
        csvFiles[currentCsvIndex].data = csvTable.getData();
        updateCsvFilesList();
        showToast(`${rowCount} row(s) added successfully`);
        modalInstance.hide();
        modal.addEventListener('hidden.bs.modal', () => modal.remove());
    });

    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}
function deleteSelectedRows() {
    const selectedRows = csvTable.getSelectedRows();
    if (selectedRows.length === 0) {
        showToast("No rows selected", "info");
        return;
    }
    if (confirm(`Are you sure you want to delete ${selectedRows.length} selected row(s)?`)) {
        selectedRows.forEach(row => row.delete());
        showToast(`${selectedRows.length} row(s) deleted successfully`);
        if (currentCsvIndex >= 0) {
            csvFiles[currentCsvIndex].data = csvTable.getData();
            updateCsvFilesList();
        }
    }
}
function duplicateSelectedRows() {
    const selectedRows = csvTable.getSelectedRows();
    if (selectedRows.length === 0) {
        showToast("No rows selected", "info");
        return;
    }
    const rowsData = selectedRows.map(row => row.getData());
    rowsData.forEach(rowData => {
        csvTable.addRow({ ...rowData });
    });
    showToast(`${selectedRows.length} row(s) duplicated successfully`);
    if (currentCsvIndex >= 0) {
        csvFiles[currentCsvIndex].data = csvTable.getData();
        updateCsvFilesList();
    }
}
function editSelectedRows() {
    const selectedRows = csvTable.getSelectedRows();
    if (selectedRows.length === 0) {
        showToast("No rows selected", "info");
        return;
    }
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'bulkEditModal';
    modal.setAttribute('aria-labelledby', 'bulkEditModalLabel');
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-modal', 'true');
    const columns = csvTable.getColumns()
        .filter(col => !['rownum', 'rowSelection'].includes(col.getField()))
        .map(col => ({
            field: col.getField(),
            title: col.getDefinition().title || col.getField()
        }));
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content p-3">
                <div class="modal-header p-3 border-bottom mb-3">
                    <h5 class="modal-title h6 mb-0" id="bulkEditModalLabel">Edit ${selectedRows.length} Selected Row(s)</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <form id="bulkEditForm">
                        <div class="mb-3">
                            <label for="columnSelect" class="form-label">Select Column to Edit</label>
                            <select class="form-select" id="columnSelect">
                                <option value="">-- Select Column --</option>
                                ${columns.map(col => `<option value="${col.field}">${col.title}</option>`).join('')}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="newValue" class="form-label">New Value</label>
                            <textarea class="form-control" id="newValue" rows="3" placeholder="Enter new value"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="applyBulkEdit">Apply Changes</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    document.getElementById('applyBulkEdit').addEventListener('click', function () {
        const columnField = document.getElementById('columnSelect').value;
        const newValue = document.getElementById('newValue').value;
        if (!columnField) {
            showToast("Please select a column", "warning");
            return;
        }
        selectedRows.forEach(row => {
            row.update({ [columnField]: newValue });
        });
        if (currentCsvIndex >= 0) {
            csvFiles[currentCsvIndex].data = csvTable.getData();
        }
        modalInstance.hide();
        modal.addEventListener('hidden.bs.modal', function () {
            modal.remove();
        });
        showToast(`Updated ${selectedRows.length} rows successfully`);
    });
    modal.addEventListener('hidden.bs.modal', function () {
        modal.remove();
    });
}
function exportDataToCSV(data, templateName) {
    const csv = Papa.unparse(data, {
        delimiter: ",",
        header: true
    });
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${templateName}_export.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    showToast(`Exported to ${templateName} template successfully`);
}
function deleteAllCsvFiles() {
    if (csvFiles.length === 0) {
        showToast("No CSV files to delete", "info");
        return;
    }
    if (confirm(`Are you sure you want to delete all ${csvFiles.length} CSV files?`)) {
        csvFiles = [];
        currentCsvIndex = -1;
        csvTable.clearData();
        csvTable.setColumns([]);
        document.getElementById('currentCsvTitle').textContent = ' CSV Table';
        updateCsvFilesList();
        showToast("All CSV files deleted successfully");
    }
}
// This function is a stub for backward compatibility
function deleteAllTemplatesStub() {
    console.log('deleteAllTemplatesStub called from csv-manager.js');
    // Call the actual implementation in csv-template.js
    if (typeof window.deleteAllTemplates === 'function') {
        window.deleteAllTemplates();
    } else {
        console.error('deleteAllTemplates function not found in window object');
    }
}

// Table templates for new table creation
const tableTemplates = {
    'Shutterstock': {
      headers: ['Filename', 'Description', 'Keywords', 'Categories', 'Editorial', 'Mature content', 'illustration'],
      sampleData: [
        ['footage_filename.mov', 'A short and simple description', 'keyword 1,keyword', 'nature,transportation', 'no', 'yes', 'no'],
        ['footage_filename_1.mpg', 'Description', 'footage,file', '', 'no', '']
      ]
    },
    'Adobe Stock': {
      headers: ['Filename', 'Title', 'Keywords', 'Category', 'Releases'],
      sampleData: [
        ['image_filename.jpg', 'A short description of what the asset represents', 'Keyword1, Keyword2, Keyword3, Keyword4, Keyword5', '3', 'Haleeq Whitten, Ludovic Hillion, Morgan Greentstreet, Christine Manore'],
        ['footage_filename.mov', 'Up to 200 characters', 'Most important keywords first. Max 50 keywords.', 'Enter the number matching the category in the upload-CSV dialog', 'The names you gave to the releases when you uploaded them on Adobe Stock']
      ]
    },
    'Canva': {
      headers: ['filename', 'title', 'keywords', 'Artist', 'locale', 'description'],
      sampleData: [
        ['sample_filename.jpg', 'Title for the sample image', 'sample,sample photo,stock photo', 'canva samples', 'en', 'This is a sample description.']
      ]
    },
    'Freepik': {
      headers: ['Filename', 'Title', 'Keywords', 'Description', 'Model'],
      sampleData: [
        ['beautiful-sunset.jpg', 'Beautiful sunset', 'sunset,sun,summer,beach,mountain', 'Beautiful sunset on the beach in summer', 'Midjourney 5'],
        ['abstract-background.jpg', 'Abstract polygonal background', 'abstract,polygonal,poly,polygon,polygons,low poly', '', ''],
        ['business-card.jpg', 'Business Card', 'cards,business,employment,business cards', '', '']
      ]
    },
    'Vecteezy': {
      headers: ['Filename', 'Title', 'Description', 'Keywords', 'License'],
      sampleData: [
        ['enter_file_name_1', 'Example Title 1', 'Example Description 2', 'enter,keywords,here', 'free'],
        ['enter_file_name_2', 'Example Title 2', 'Example Description 2', 'enter,keywords,here', 'pro'],
        ['enter_file_name_3', 'Example Title 3', 'Example Description 3', 'enter,keywords,here', 'editorial']
      ]
    },
    'Dreamstime': {
      headers: ['Filename', 'Image Name', 'Description', 'Category 1', 'Category 2', 'Category 3', 'keywords', 'Free', 'W-EL', 'P-EL', 'SR-EL', 'SR-Price', 'Editorial', 'MR doc Ids', 'Pr Docs'],
      sampleData: [
        ['Dreamstime_1234.jpg', 'Perfect start of a dream', 'Dreamstime, a photo`s dream come true', '165', '0', '0', 'test1,test2,test3', '1', '1', '0', '0', '0', '1', '252233,252234', '1358']
      ]
    }
  };

// Show the modal for creating a new table
function showNewTableModal() {
    // Check if the DOM is fully loaded
    if (document.readyState === 'loading') {
        // If the DOM is still loading, wait for it to be ready
        document.addEventListener('DOMContentLoaded', showNewTableModal);
        console.log('DOM still loading, waiting for DOMContentLoaded event');
        return;
    }

    // First, ensure the modal exists
    ensureNewTableModalExists();

    // Get the modal element
    const modal = document.getElementById('newTableModal');

    // If modal still doesn't exist after trying to create it, show error
    if (!modal) {
        console.error("Failed to find or create New Table Modal.");
        showToast('Error: Could not create New Table Modal. Please refresh the page and try again.', 'error');
        return;
    }

    try {
        // Clear previous inputs
        const headerInputs = document.getElementById('headerInputs');
        if (headerInputs) {
            headerInputs.innerHTML = '';
        } else {
            console.warn('Header inputs container not found');
        }

        const tableNameInput = document.getElementById('tableNameInput');
        if (tableNameInput) {
            tableNameInput.value = '';
        } else {
            console.warn('Table name input not found');
        }

        // Set template select to Custom/Costum
        const templateSelect = document.getElementById('tableTemplateSelect');
        if (templateSelect) {
            try {
                // Check if 'Custom' option exists
                const customOption = Array.from(templateSelect.options).find(option =>
                    option.value === 'Custom' || option.value === 'Costum');

                if (customOption) {
                    templateSelect.value = customOption.value;
                } else {
                    // Default to first option if neither exists
                    templateSelect.selectedIndex = 0;
                }
            } catch (error) {
                console.warn('Error setting template select value:', error);
            }
        } else {
            console.warn('Template select not found');
        }

        // Set up event listeners
        setupNewTableModalEvents();

        // Show the modal
        try {
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();
            } else {
                console.error('Bootstrap Modal is not available');
                showToast('Error: Bootstrap Modal is not available', 'error');
            }
        } catch (modalError) {
            console.error('Error showing modal:', modalError);
            showToast('Error showing modal: ' + modalError.message, 'error');
        }
    } catch (error) {
        console.error('Error showing new table modal:', error);
        showToast('Error showing new table modal. Please try again.', 'error');
    }
}

// Set up event listeners for the new table modal
function setupNewTableModalEvents() {
    try {
        // Add header button
        const addHeaderBtn = document.getElementById('addHeaderBtn');
        if (addHeaderBtn) {
            // Remove existing event listeners to prevent duplicates
            const newAddHeaderBtn = addHeaderBtn.cloneNode(true);
            addHeaderBtn.parentNode.replaceChild(newAddHeaderBtn, addHeaderBtn);

            newAddHeaderBtn.addEventListener('click', addHeaderInput);
        } else {
            console.warn('Add Header Button not found');
        }

        // Template select change
        const tableTemplateSelect = document.getElementById('tableTemplateSelect');
        if (tableTemplateSelect) {
            // Remove existing event listeners to prevent duplicates
            const newTableTemplateSelect = tableTemplateSelect.cloneNode(true);
            tableTemplateSelect.parentNode.replaceChild(newTableTemplateSelect, tableTemplateSelect);

            newTableTemplateSelect.addEventListener('change', function() {
                try {
                    const selectedTemplate = this.value;
                    const headerInputs = document.getElementById('headerInputs');

                    if (!headerInputs) {
                        console.warn('Header Inputs container not found');
                        return;
                    }

                    if (selectedTemplate === 'Custom' || selectedTemplate === 'Costum') {
                        headerInputs.innerHTML = '';
                    } else if (selectedTemplate && tableTemplates[selectedTemplate]) {
                        loadTableTemplate(selectedTemplate);
                    }
                } catch (error) {
                    console.error('Error in template change handler:', error);
                }
            });
        } else {
            console.warn('Table Template Select not found');
        }

        // Create table button
        const createTableButton = document.getElementById('createTableButton');
        if (createTableButton) {
            // Remove existing event listeners to prevent duplicates
            const newCreateTableButton = createTableButton.cloneNode(true);
            createTableButton.parentNode.replaceChild(newCreateTableButton, createTableButton);

            newCreateTableButton.addEventListener('click', function() {
                try {
                    const tableNameInput = document.getElementById('tableNameInput');
                    if (!tableNameInput) {
                        showToast('Table name input not found', 'error');
                        return;
                    }

                    const tableName = tableNameInput.value.trim();

                    const headerInputsContainer = document.getElementById('headerInputs');
                    if (!headerInputsContainer) {
                        showToast('Header inputs container not found', 'error');
                        return;
                    }

                    const headerInputs = headerInputsContainer.querySelectorAll('input');
                    const headers = Array.from(headerInputs).map(input => input.value.trim());

                    // Validate inputs
                    if (tableName === '') {
                        showToast('Please enter a table name', 'error');
                        return;
                    }

                    if (headers.length === 0) {
                        showToast('Please add at least one header', 'error');
                        return;
                    }

                    if (headers.some(header => header === '')) {
                        showToast('Please fill in all header fields', 'error');
                        return;
                    }

                    // Create the table
                    const templateSelect = document.getElementById('tableTemplateSelect');
                    if (!templateSelect) {
                        showToast('Template select not found', 'error');
                        return;
                    }

                    const selectedTemplate = templateSelect.value;
                    let sampleData = [];

                    if (selectedTemplate !== 'Custom' && selectedTemplate !== 'Costum' && tableTemplates[selectedTemplate]) {
                        // Use sample data from template if available
                        sampleData = tableTemplates[selectedTemplate].sampleData.map(row => {
                            const rowObj = {};
                            headers.forEach((header, index) => {
                                rowObj[header] = row[index] || '';
                            });
                            return rowObj;
                        });
                    } else {
                        // Create an empty row
                        const emptyRow = {};
                        headers.forEach(header => {
                            emptyRow[header] = '';
                        });
                        sampleData.push(emptyRow);
                    }

                    createNewTable(tableName, headers, sampleData);

                    // Close the modal properly
                    const modal = document.getElementById('newTableModal');
                    if (modal) {
                        try {
                            // Get the modal instance using Bootstrap's API
                            const modalInstance = bootstrap.Modal.getInstance(modal);
                            if (modalInstance) {
                                // Use Bootstrap's hide method which handles cleanup properly
                                modalInstance.hide();
                            } else {
                                console.warn('Modal instance not found, hiding using Bootstrap');
                                // Create a new instance and hide it
                                const newModalInstance = new bootstrap.Modal(modal);
                                newModalInstance.hide();
                            }
                        } catch (error) {
                            console.error('Error hiding modal:', error);
                            // Fallback to manual hiding as a last resort
                            try {
                                modal.style.display = 'none';
                                modal.classList.remove('show');
                                modal.setAttribute('aria-hidden', 'true');
                                modal.removeAttribute('aria-modal');
                                modal.removeAttribute('role');

                                const backdrop = document.querySelector('.modal-backdrop');
                                if (backdrop) backdrop.remove();

                                document.body.classList.remove('modal-open');
                                document.body.style.overflow = '';
                                document.body.style.paddingRight = '';
                            } catch (fallbackError) {
                                console.error('Error in fallback modal hiding:', fallbackError);
                            }
                        }
                    } else {
                        console.warn('Modal element not found for hiding');
                    }
                } catch (error) {
                    console.error('Error in create table handler:', error);
                    showToast('Error creating table: ' + error.message, 'error');
                }
            });
        } else {
            console.warn('Create Table Button not found');
        }

        // Cancel button
        const cancelTableButton = document.getElementById('cancelTableButton');
        if (cancelTableButton) {
            // Remove existing event listeners to prevent duplicates
            const newCancelTableButton = cancelTableButton.cloneNode(true);
            cancelTableButton.parentNode.replaceChild(newCancelTableButton, cancelTableButton);
        } else {
            console.warn('Cancel Table Button not found');
        }

        // Add initial header input if none exist
        const headerInputs = document.getElementById('headerInputs');
        if (headerInputs && headerInputs.children.length === 0) {
            addHeaderInput();
        }
    } catch (error) {
        console.error('Error setting up new table modal events:', error);
        showToast('Error setting up new table modal. Please try again.', 'error');
    }
}

// Add a new header input field
function addHeaderInput() {
    try {
        const headerInputs = document.getElementById('headerInputs');
        if (!headerInputs) {
            console.error('Header inputs container not found');
            return;
        }

        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'form-control';
        input.placeholder = 'Enter header name';
        headerInputs.appendChild(input);

        try {
            input.focus();
        } catch (focusError) {
            console.warn('Could not focus on new input:', focusError);
        }
    } catch (error) {
        console.error('Error adding header input:', error);
    }
}

// Load a table template
function loadTableTemplate(templateName) {
    try {
        const template = tableTemplates[templateName];
        if (!template) {
            console.warn(`Template "${templateName}" not found`);
            return;
        }

        const headerInputs = document.getElementById('headerInputs');
        if (!headerInputs) {
            console.error('Header inputs container not found');
            return;
        }

        headerInputs.innerHTML = '';

        if (!template.headers || !Array.isArray(template.headers)) {
            console.warn(`Template "${templateName}" has no valid headers`);
            return;
        }

        template.headers.forEach(header => {
            try {
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control';
                input.value = header || '';
                input.placeholder = 'Enter header name';
                headerInputs.appendChild(input);
            } catch (inputError) {
                console.error('Error creating header input:', inputError);
            }
        });
    } catch (error) {
        console.error('Error loading table template:', error);
    }
}

// Function to create the New Table Modal dynamically if it doesn't exist
function createNewTableModal() {
    try {
        // Check if the modal already exists
        if (document.getElementById('newTableModal')) {
            console.log('New Table Modal already exists, not creating a duplicate');
            return;
        }

        console.log('Creating New Table Modal dynamically');

        // Create the modal element
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'newTableModal';
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('aria-labelledby', 'newTableModalLabel');
        // Don't set aria-hidden to prevent accessibility issues
        modal.setAttribute('data-accessibility-fix', 'true');

        // Set the modal HTML content
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: var(--primary-purple); color: white;">
                        <h5 class="modal-title" id="newTableModalLabel">Create New Table</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="tableTemplateSelect" class="form-label">Select Template:</label>
                            <select id="tableTemplateSelect" class="form-select">
                                <option value="Custom" selected>Custom</option>
                                <option value="Shutterstock">Shutterstock</option>
                                <option value="Adobe Stock">Adobe Stock</option>
                                <option value="Canva">Canva</option>
                                <option value="Freepik">Freepik</option>
                                <option value="Vecteezy">Vecteezy</option>
                                <option value="Dreamstime">Dreamstime</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="tableNameInput" class="form-label">Table Name:</label>
                            <input type="text" class="form-control" id="tableNameInput" placeholder="Enter table name">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Headers:</label>
                            <div id="headerInputs" class="header-inputs">
                                <!-- Header inputs will be added here -->
                            </div>
                            <button type="button" class="btn btn-primary mt-2" id="addHeaderBtn">
                                <i class="bi bi-plus-circle"></i> Add Header
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancelTableButton" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn" id="createTableButton" style="background-color: var(--primary-purple); color: white;">Create Table</button>
                    </div>
                </div>
            </div>
        `;

        // Append the modal to the body
        document.body.appendChild(modal);
        console.log('New Table Modal created and added to DOM');

    } catch (error) {
        console.error('Error creating New Table Modal:', error);
    }
}

// Create a new table with the given name, headers, and sample data
function createNewTable(tableName, headers, sampleData) {
    try {
        // Create columns configuration
        const columns = [
            {
                formatter: "rownum",
                hozAlign: "center",
                headerSort: false,
                width: 40,
                title: "No.",
                download: false,
                print: false
            },
            {
                formatter: "rowSelection",
                titleFormatter: "rowSelection",
                titleFormatterParams: {
                    rowRange: "active"
                },
                hozAlign: "center",
                headerSort: false,
                width: 40,
                download: false,
                print: false
            }
        ];

        // Add columns for each header
        headers.forEach(header => {
            columns.push({
                title: header,
                field: header,
                editor: "input",
                headerFilter: "input"
            });
        });

        // Create a new CSV file object
        const csvFile = {
            name: tableName + '.csv',
            data: sampleData,
            columns: columns.slice(2) // Exclude rownum and rowSelection columns
        };

        // Add to csvFiles array
        if (!Array.isArray(csvFiles)) {
            console.error('csvFiles is not an array');
            showToast('Error creating table: csvFiles is not an array', 'error');
            return;
        }

        csvFiles.push(csvFile);
        currentCsvIndex = csvFiles.length - 1;

        // Update UI
        try {
            updateCsvFilesList();
        } catch (updateError) {
            console.error('Error updating CSV files list:', updateError);
        }

        try {
            displayCsvFile(currentCsvIndex);
        } catch (displayError) {
            console.error('Error displaying CSV file:', displayError);
        }

        showToast(`New table "${tableName}" created successfully`, 'success');
    } catch (error) {
        console.error('Error creating new table:', error);
        showToast('Error creating new table: ' + error.message, 'error');
    }
}
