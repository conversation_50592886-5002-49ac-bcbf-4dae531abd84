// Make functions available globally
window.deleteAllTemplates = deleteAllTemplates;
window.addTemplate = addTemplate;
window.deleteTemplate = deleteTemplate;
window.toggleTemplate = toggleTemplate;
window.exportTemplate = exportTemplate;
window.exportTemplateAsJson = exportTemplateAsJson;
window.exportAllTemplatesAsJson = exportAllTemplatesAsJson;
window.importTemplate = importTemplate;
window.importTemplateFromJson = importTemplateFromJson;
window.loadTemplate = loadTemplate;
window.saveTemplate = saveTemplate;
window.isValidTemplate = isValidTemplate;

/**
 * Extract category number from display format
 * @param {string} categoryDisplay - The category display format
 * @returns {string} - Just the category number
 */
function extractAdobeStockCategoryNumber(categoryDisplay) {
    if (!categoryDisplay || categoryDisplay.trim() === '') {
        return '';
    }

    // Extract number from format like "1. Animals" or just "1"
    const numberMatch = categoryDisplay.trim().match(/^(\d+)/);
    return numberMatch ? numberMatch[1] : categoryDisplay.trim();
}

function addTemplate(name, fields) {
    if (!name || !fields || !Array.isArray(fields)) {
        showToast("Invalid template data", "error");
        return false;
    }

    const template = {
        name: name,
        fields: fields,
        active: true,
        dateAdded: new Date().toISOString()
    };

    csvTemplates.push(template);
    saveTemplates();
    updateTemplateList();
    updateTemplateButtons();
    showToast(`Template "${name}" added successfully`);
    return true;
}

function toggleTemplate(templateId) {
    const index = parseInt(templateId);
    if (isNaN(index) || index < 0 || index >= csvTemplates.length) {
        showToast("Invalid template ID", "error");
        return false;
    }

    csvTemplates[index].active = !csvTemplates[index].active;
    saveTemplates();
    updateTemplateList();
    updateTemplateButtons();
    showToast(`Template "${csvTemplates[index].name}" ${csvTemplates[index].active ? 'activated' : 'deactivated'} successfully`);
    return true;
}

function exportTemplate(template) {
    if (!template || !template.fields) {
        showToast("Invalid template data", "error");
        return false;
    }

    // Export as CSV (original functionality)
    const csvData = [template.fields];
    const csv = Papa.unparse(csvData, {
        delimiter: ",",
        header: true
    });
    const csvBlob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const csvUrl = URL.createObjectURL(csvBlob);
    const csvLink = document.createElement('a');
    csvLink.href = csvUrl;
    csvLink.setAttribute('download', `${template.name}_template.csv`);
    document.body.appendChild(csvLink);
    csvLink.click();
    document.body.removeChild(csvLink);
    URL.revokeObjectURL(csvUrl);

    showToast(`Template "${template.name}" exported successfully`);
    return true;
}

function exportTemplateAsJson(template) {
    if (!template || !template.fields) {
        showToast("Invalid template data", "error");
        return false;
    }

    // Create a copy of the template to export
    const templateToExport = {
        name: template.name,
        fields: template.fields,
        active: template.active,
        dateAdded: template.dateAdded || new Date().toISOString()
    };

    // Convert to JSON
    const jsonContent = JSON.stringify(templateToExport, null, 2);
    const jsonBlob = new Blob([jsonContent], { type: 'application/json' });
    const jsonUrl = URL.createObjectURL(jsonBlob);
    const jsonLink = document.createElement('a');
    jsonLink.href = jsonUrl;
    jsonLink.setAttribute('download', `${template.name}_template.json`);
    document.body.appendChild(jsonLink);
    jsonLink.click();
    document.body.removeChild(jsonLink);
    URL.revokeObjectURL(jsonUrl);

    showToast(`Template "${template.name}" exported as JSON successfully`);
    return true;
}

function exportAllTemplatesAsJson() {
    if (!csvTemplates || csvTemplates.length === 0) {
        showToast("No templates to export", "error");
        return false;
    }

    // Create a copy of all templates to export
    const templatesToExport = csvTemplates.map(template => ({
        name: template.name,
        fields: template.fields,
        active: template.active,
        dateAdded: template.dateAdded || new Date().toISOString()
    }));

    // Convert to JSON
    const jsonContent = JSON.stringify(templatesToExport, null, 2);
    const jsonBlob = new Blob([jsonContent], { type: 'application/json' });
    const jsonUrl = URL.createObjectURL(jsonBlob);
    const jsonLink = document.createElement('a');
    jsonLink.href = jsonUrl;
    jsonLink.setAttribute('download', `csvision_templates.json`);
    document.body.appendChild(jsonLink);
    jsonLink.click();
    document.body.removeChild(jsonLink);
    URL.revokeObjectURL(jsonUrl);

    showToast(`All templates exported as JSON successfully`);
    return true;
}

function showTemplateImport() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv,.json';
    input.multiple = true;
    input.style.display = 'none';
    input.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            Array.from(e.target.files).forEach(file => {
                const fileExtension = file.name.split('.').pop().toLowerCase();
                if (fileExtension === 'csv') {
                    importTemplate(file);
                } else if (fileExtension === 'json') {
                    importTemplateFromJson(file);
                } else {
                    showToast(`Unsupported file type: ${fileExtension}`, "error");
                }
            });
        }
    });
    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
}

function importTemplateFromJson(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const content = e.target.result;
            const jsonData = JSON.parse(content);

            // Check if it's a single template or an array of templates
            if (Array.isArray(jsonData)) {
                // It's an array of templates
                let importedCount = 0;
                jsonData.forEach(template => {
                    if (isValidTemplate(template)) {
                        // Check if template with same name already exists
                        const existingIndex = csvTemplates.findIndex(t => t.name === template.name);
                        if (existingIndex >= 0) {
                            // Update existing template
                            csvTemplates[existingIndex] = {
                                ...template,
                                dateAdded: template.dateAdded || new Date().toISOString()
                            };
                        } else {
                            // Add new template
                            csvTemplates.push({
                                ...template,
                                dateAdded: template.dateAdded || new Date().toISOString()
                            });
                        }
                        importedCount++;
                    }
                });

                if (importedCount > 0) {
                    saveTemplates();
                    updateTemplateList();
                    updateTemplateButtons();
                    showToast(`Imported ${importedCount} templates successfully`);
                } else {
                    showToast("No valid templates found in the file", "error");
                }
            } else if (isValidTemplate(jsonData)) {
                // It's a single template
                const template = jsonData;

                // Check if template with same name already exists
                const existingIndex = csvTemplates.findIndex(t => t.name === template.name);
                if (existingIndex >= 0) {
                    // Update existing template
                    csvTemplates[existingIndex] = {
                        ...template,
                        dateAdded: template.dateAdded || new Date().toISOString()
                    };
                } else {
                    // Add new template
                    csvTemplates.push({
                        ...template,
                        dateAdded: template.dateAdded || new Date().toISOString()
                    });
                }

                saveTemplates();
                updateTemplateList();
                updateTemplateButtons();
                showToast(`Template "${template.name}" imported successfully`);
            } else {
                showToast("Invalid template format", "error");
            }
        } catch (error) {
            console.error("Error parsing JSON template:", error);
            showToast("Error importing template: " + error.message, "error");
        }
    };
    reader.readAsText(file);
}

function isValidTemplate(template) {
    return template &&
           typeof template === 'object' &&
           typeof template.name === 'string' &&
           Array.isArray(template.fields) &&
           template.fields.length > 0;
}
function importTemplate(file) {
    Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        preview: 1, // Only parse the first row to get headers
        complete: function (results) {
            if (results.meta.fields && results.meta.fields.length > 0) {
                const templateName = file.name.replace(/\.[^/.]+$/, "");
                const template = {
                    name: templateName,
                    fields: results.meta.fields,
                    active: true,
                    dateAdded: new Date().toISOString()
                };
                csvTemplates.push(template);
                saveTemplates();
                updateTemplateList();
                updateTemplateButtons();
                showToast(`Template "${templateName}" imported successfully`);
            } else {
                showToast("Invalid template format. CSV must contain headers.", "error");
            }
        },
        error: function (error) {
            console.error("Error parsing CSV template:", error);
            showToast("Error importing template", "error");
        }
    });
}
function saveTemplates() {
    // Ensure window.csvTemplates is updated
    window.csvTemplates = csvTemplates;

    // Save to localStorage
    localStorage.setItem('csvision_templates', JSON.stringify(csvTemplates));

    console.log('Templates saved to localStorage, count:', csvTemplates.length);
}
function loadTemplates() {
    console.log('Loading templates...');

    if (window.csvTemplates && window.csvTemplates.length > 0) {
        console.log('Using templates from window.csvTemplates, count:', window.csvTemplates.length);
        csvTemplates = window.csvTemplates;
    } else {
        const savedTemplates = localStorage.getItem('csvision_templates');
        if (savedTemplates) {
            try {
                csvTemplates = JSON.parse(savedTemplates);
                window.csvTemplates = csvTemplates;
                console.log('Loaded templates from localStorage, count:', csvTemplates.length);
            } catch (error) {
                console.error('Error parsing saved templates:', error);
                csvTemplates = [];
                window.csvTemplates = [];
            }
        } else {
            console.log('No saved templates found');
            csvTemplates = [];
            window.csvTemplates = [];
        }
    }

    // Always ensure csvTemplates and window.csvTemplates are in sync
    window.csvTemplates = csvTemplates;

    updateTemplateList();
    updateTemplateButtons();
}
function updateTemplateList() {
    const templateList = document.getElementById('templateList');
    if (!templateList) return;
    templateList.innerHTML = '';
    if (csvTemplates.length === 0) {
        templateList.innerHTML = '<div class="list-group-item text-center text-muted">No templates available</div>';
        updateTemplateCounters();
        return;
    }
    const activeTemplates = csvTemplates.filter(t => t.active).length;
    const inactiveTemplates = csvTemplates.length - activeTemplates;
    const headerItem = document.createElement('div');
    headerItem.className = 'list-group-item d-flex justify-content-between align-items-center bg-light';
    const headerLeft = document.createElement('div');
    headerLeft.className = 'd-flex align-items-center';
    const toggleAllSwitch = document.createElement('div');
    toggleAllSwitch.className = 'form-check form-switch me-3';
    const toggleAllInput = document.createElement('input');
    toggleAllInput.type = 'checkbox';
    toggleAllInput.className = 'form-check-input';
    toggleAllInput.checked = activeTemplates === csvTemplates.length;
    toggleAllInput.addEventListener('change', function() {
        const newState = this.checked;
        csvTemplates.forEach(t => t.active = newState);
        saveTemplates();
        updateTemplateList();
        updateTemplateCounters();
    });
    toggleAllSwitch.appendChild(toggleAllInput);
    const headerTitle = document.createElement('span');
    headerTitle.className = 'fw-bold';
    headerTitle.textContent = 'Templates';
    headerLeft.appendChild(toggleAllSwitch);
    headerLeft.appendChild(headerTitle);
    const headerRight = document.createElement('div');
    headerRight.className = 'd-flex align-items-center gap-2';
    const inactiveBadge = document.createElement('span');
    inactiveBadge.className = 'badge bg-secondary';
    inactiveBadge.title = 'Inactive templates';
    inactiveBadge.innerHTML = `<i class="bi bi-dash-circle"></i> <span id="inactiveTemplateCount">${inactiveTemplates}</span>`;
    const activeBadge = document.createElement('span');
    activeBadge.className = 'badge bg-success';
    activeBadge.title = 'Active templates';
    activeBadge.innerHTML = `<i class="bi bi-check-circle"></i> <span id="activeTemplateCount">${activeTemplates}</span>`;
    const totalBadge = document.createElement('span');
    totalBadge.className = 'badge bg-primary';
    totalBadge.title = 'Total templates';
    totalBadge.innerHTML = `<i class="bi bi-files"></i> <span id="totalTemplateCount">${csvTemplates.length}</span>`;
    headerRight.appendChild(inactiveBadge);
    headerRight.appendChild(activeBadge);
    headerRight.appendChild(totalBadge);
    headerItem.appendChild(headerLeft);
    headerItem.appendChild(headerRight);
    templateList.appendChild(headerItem);
    csvTemplates.forEach((template, index) => {
        const item = document.createElement('div');
        item.className = 'list-group-item d-flex justify-content-between align-items-center template-item';
        item.id = `template-${index}`;
        item.dataset.templateId = index;
        const leftSection = document.createElement('div');
        leftSection.className = 'd-flex align-items-center';
        const toggleSwitch = document.createElement('div');
        toggleSwitch.className = 'form-check form-switch me-3';
        const toggleInput = document.createElement('input');
        toggleInput.type = 'checkbox';
        toggleInput.className = 'form-check-input template-status-toggle';
        toggleInput.checked = template.active;
        toggleInput.addEventListener('change', function() {
            template.active = this.checked;
            saveTemplates();
            updateTemplateList();
            updateTemplateButtons();
        });
        toggleSwitch.appendChild(toggleInput);
        const templateName = document.createElement('span');
        templateName.className = 'template-name';
        templateName.textContent = template.name;
        leftSection.appendChild(toggleSwitch);
        leftSection.appendChild(templateName);
        const rightSection = document.createElement('div');
        rightSection.className = 'd-flex align-items-center gap-2';
        const statusBadge = document.createElement('span');
        statusBadge.className = `badge ${template.active ? 'bg-success' : 'bg-secondary'}`;
        statusBadge.innerHTML = template.active ?
            '<i class="bi bi-check-circle"></i> Active' :
            '<i class="bi bi-dash-circle"></i> Inactive';
        const fieldsCount = document.createElement('span');
        fieldsCount.className = 'badge bg-info';
        fieldsCount.innerHTML = `<i class="bi bi-table"></i> ${template.fields ? template.fields.length : 0}`;
        fieldsCount.title = 'Number of fields';

        // Add export JSON button
        const exportJsonBtn = document.createElement('button');
        exportJsonBtn.className = 'btn btn-sm btn-outline-info export-json-btn';
        exportJsonBtn.innerHTML = '<i class="bi bi-file-earmark-code"></i>';
        exportJsonBtn.title = 'Export as JSON';
        exportJsonBtn.dataset.templateId = index;
        exportJsonBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            exportTemplateAsJson(template);
        });

        // Add delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'btn btn-sm btn-outline-danger delete-template-btn';
        deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
        deleteBtn.title = 'Delete template';

        rightSection.appendChild(statusBadge);
        rightSection.appendChild(fieldsCount);
        rightSection.appendChild(exportJsonBtn);
        rightSection.appendChild(deleteBtn);
        item.appendChild(leftSection);
        item.appendChild(rightSection);
        templateList.appendChild(item);
    });
    updateTemplateCounters();
}
function updateTemplateButtons() {
    const existingButtons = document.querySelectorAll('.template-export-btn');
    existingButtons.forEach(btn => btn.remove());
    const btnGroup = document.querySelector('#content .btn-group');
    if (btnGroup && csvTemplates.length > 0) {
        const hasMetadataResults = window.metadataResults && window.metadataResults.length > 0;
        const hasCsvData = window.csvFiles && window.csvFiles.length > 0 && window.currentCsvIndex >= 0;
        const hasData = hasMetadataResults || hasCsvData;
        const activeTemplates = csvTemplates.filter(template => template.active);
        if (activeTemplates.length > 4) {
            for (let i = 0; i < 3; i++) {
                const template = activeTemplates[i];
                const btn = document.createElement('button');
                btn.className = 'btn btn-info template-export-btn bi bi-download';
                btn.disabled = !hasData;
                btn.innerHTML = ` ${template.name}`;
                btn.addEventListener('click', () => exportToTemplate(template));
                btnGroup.appendChild(btn);
            }
            const hiddenCount = activeTemplates.length - 3;
            const moreBtn = document.createElement('button');
            moreBtn.className = 'btn btn-info template-export-btn bi bi-three-dots';
            moreBtn.disabled = !hasData;
            moreBtn.innerHTML = ` More <span class="badge bg-light text-dark">+${hiddenCount}</span>`;
            moreBtn.onclick = function() {
                showMoreTemplatesModal();
            };
            btnGroup.appendChild(moreBtn);
        } else {
            activeTemplates.forEach(template => {
                const btn = document.createElement('button');
                btn.className = 'btn btn-info template-export-btn bi bi-download';
                btn.disabled = !hasData;
                btn.innerHTML = ` ${template.name}`;
                btn.addEventListener('click', () => exportToTemplate(template));
                btnGroup.appendChild(btn);
            });
        }
    }
}
function showMoreTemplatesModal() {
    let modal = document.getElementById('moreTemplatesModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'moreTemplatesModal';
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('aria-labelledby', 'moreTemplatesModalLabel');
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="moreTemplatesModalLabel">Template Manager</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row" id="templateModalContent">
                            <!-- Template items will be inserted here dynamically -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <span class="template-count" id="templateCountBadge">0 Templates</span>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    const templateModalContent = document.getElementById('templateModalContent');
    if (!templateModalContent) {
        console.error("Modal content element not found: templateModalContent");
        return;
    }

    const hasMetadataResults = window.metadataResults && window.metadataResults.length > 0;
    const hasCsvData = csvFiles && csvFiles.length > 0 && currentCsvIndex >= 0;
    const hasData = hasMetadataResults || hasCsvData;
    const activeTemplates = csvTemplates.filter(template => template.active);
    const templateCountBadge = document.getElementById('templateCountBadge');

    if (templateCountBadge) {
        templateCountBadge.textContent = `${activeTemplates.length} Templates`;
    }

    templateModalContent.innerHTML = activeTemplates.length > 0 ?
        activeTemplates.map((template, index) => `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">${template.name}</h6>
                        <div class="card-text">
                            <small class="text-muted">
                                ${template.fields ? template.fields.map(field => `<span class="badge bg-light text-dark me-1 mb-1">${field}</span>`).join('') : ''}
                            </small>
                        </div>
                        <button class="btn btn-sm btn-primary w-100 mt-2 template-modal-btn" ${!hasData ? 'disabled' : ''} data-template-index="${index}">
                            <i class="bi bi-file-earmark-arrow-down"></i> Export to this template
                        </button>
                    </div>
                </div>
            </div>
        `).join('') :
        '<div class="col-12 text-center py-4"><p class="text-muted">No templates available. Create a template first.</p></div>';

    const modalButtons = modal.querySelectorAll('.template-modal-btn');
    modalButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const templateIndex = parseInt(btn.getAttribute('data-template-index'));
            if (!isNaN(templateIndex) && templateIndex >= 0 && templateIndex < activeTemplates.length) {
                exportToTemplate(activeTemplates[templateIndex]);
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    });

    const bsModal = new bootstrap.Modal(modal, {
        backdrop: 'static',
        keyboard: false
    });

    modal.addEventListener('show.bs.modal', function () {
        document.body.style.overflow = 'hidden';
        document.body.style.paddingRight = '17px';
    });

    modal.addEventListener('hidden.bs.modal', function () {
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    });

    bsModal.show();
}
function exportToTemplate(template) {
    console.log("Exporting to template:", template);
    let dataToExport = null;
    let dataSource = "";
    const tableElement = document.getElementById('metadataTable');
    if (tableElement) {
        const tabulatorInstance = Tabulator.findTable("#metadataTable")[0];
        if (tabulatorInstance) {
            try {
                const tableData = tabulatorInstance.getData();
                console.log("Metadata table data:", tableData);
                if (tableData && tableData.length > 0) {
                    dataToExport = tableData;
                    dataSource = "metadata";
                }
            } catch (error) {
                console.error("Error getting data from Tabulator instance:", error);
            }
        }
    }
    if (!dataToExport && currentCsvIndex >= 0 && csvFiles[currentCsvIndex] && csvTable) {
        try {
            const tableData = csvTable.getData();
            console.log("CSV table data:", tableData);
            if (tableData && tableData.length > 0) {
                dataToExport = tableData;
                dataSource = "csv";
            }
        } catch (error) {
            console.error("Error getting data from CSV table:", error);
        }
    }
    if (!dataToExport && typeof window.metadataResults !== 'undefined' && Array.isArray(window.metadataResults)) {
        const data = window.metadataResults;
        if (data && data.length > 0) {
            dataToExport = data;
            dataSource = "window.metadataResults";
        }
    }
    if (dataToExport && dataToExport.length > 0) {
        console.log(`Using data from ${dataSource} source with ${dataToExport.length} rows`);
        const csvData = dataToExport.map(item => {
            const row = {};
            template.fields.forEach(field => {
                const lowerField = field.toLowerCase();
                if (dataSource === "metadata" || dataSource === "window.metadataResults") {
                    if (lowerField.includes('file') || lowerField.includes('name') || lowerField === 'filename') {
                        row[field] = item.name || "";
                    } else if (lowerField.includes('title')) {
                        row[field] = item.title || "";
                    } else if (lowerField.includes('desc')) {
                        row[field] = item.description || "";
                    } else if (lowerField.includes('key') || lowerField.includes('tag')) {
                        row[field] = item.keywords || "";
                    } else if (lowerField.includes('categories') && lowerField.includes('shutterstock')) {
                        row[field] = item.shutterstockCategories || "";
                    } else if (lowerField.includes('category') && lowerField.includes('adobe')) {
                        // For Adobe Stock category, extract only the number for export
                        row[field] = extractAdobeStockCategoryNumber ? extractAdobeStockCategoryNumber(item.adobestockCategory || '') : item.adobestockCategory || "";
                    } else if (lowerField === 'categories') {
                        row[field] = item.shutterstockCategories || "";
                    } else if (lowerField === 'category') {
                        // For generic category field, assume Adobe Stock format and extract number
                        row[field] = extractAdobeStockCategoryNumber ? extractAdobeStockCategoryNumber(item.adobestockCategory || '') : item.adobestockCategory || "";
                    } else {
                        row[field] = item[field] || "";
                    }
                } else if (dataSource === "csv") {
                    row[field] = item[field] !== undefined ? item[field] : "";
                }
            });
            return row;
        });
        exportDataToCSV(csvData, template.name);
    } else {
        console.log("No data found, creating empty template");
        const emptyRow = {};
        template.fields.forEach(field => {
            emptyRow[field] = "";
        });
        exportDataToCSV([emptyRow], template.name);
    }
}

function deleteAllTemplates() {
    console.log('deleteAllTemplates called from csv-template.js');
    console.log('Current templates count:', csvTemplates.length);

    if (csvTemplates.length === 0) {
        showToast("No templates to delete", "info");
        return;
    }

    // Use Bootstrap modal instead of browser confirm
    const modalHTML = `
    <div class="modal fade" id="deleteAllTemplatesModal" tabindex="-1" aria-labelledby="deleteAllTemplatesModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteAllTemplatesModalLabel">Confirm Delete All Templates</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete all ${csvTemplates.length} templates?</p>
                    <p class="text-danger"><strong>This action cannot be undone!</strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelDeleteAllTemplates">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteAllTemplates">Delete All</button>
                </div>
            </div>
        </div>
    </div>`;

    // Remove existing modal if it exists
    const existingModal = document.getElementById('deleteAllTemplatesModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Get modal element
    const modalElement = document.getElementById('deleteAllTemplatesModal');

    // Create modal instance
    const modal = new bootstrap.Modal(modalElement);

    // Add event listener to cancel button - use once:true to ensure it only fires once
    document.getElementById('cancelDeleteAllTemplates').addEventListener('click', function() {
        modal.hide();
    }, { once: true });

    // Add event listener to confirm button - use once:true to ensure it only fires once
    document.getElementById('confirmDeleteAllTemplates').addEventListener('click', function confirmDeleteHandler() {
        // Delete all templates
        csvTemplates = [];

        // Update global variable
        window.csvTemplates = csvTemplates;

        // Save to localStorage
        saveTemplates();

        // Update UI
        updateTemplateList();
        updateTemplateButtons();

        // Hide modal
        modal.hide();

        // Show success message
        showToast("All templates deleted successfully");

        console.log('Templates deleted, new count:', csvTemplates.length);

        // Remove this event listener to prevent double-firing
        document.getElementById('confirmDeleteAllTemplates').removeEventListener('click', confirmDeleteHandler);
    }, { once: true });

    // Remove modal from DOM when hidden
    modalElement.addEventListener('hidden.bs.modal', function() {
        modalElement.remove();
    });

    // Show modal
    modal.show();
}

window.showMoreTemplatesModal = showMoreTemplatesModal;

// Initialize tooltips if they exist and haven't been initialized yet
function initTooltips() {
    const tooltipTriggerElements = document.querySelectorAll('[data-bs-toggle="tooltip"]:not(.tooltip-initialized)');
    if (tooltipTriggerElements.length > 0) {
        tooltipTriggerElements.forEach(el => {
            new bootstrap.Tooltip(el);
            el.classList.add('tooltip-initialized');
        });
    }
}

// Call initTooltips when DOM is loaded
document.addEventListener('DOMContentLoaded', initTooltips);
function updateTemplateCounters() {
    const activeCount = document.getElementById('activeTemplateCount');
    const inactiveCount = document.getElementById('inactiveTemplateCount');
    const totalCount = document.getElementById('totalTemplateCount');
    if (activeCount && inactiveCount && totalCount) {
        const active = csvTemplates.filter(t => t.active).length;
        const inactive = csvTemplates.length - active;
        activeCount.textContent = active;
        inactiveCount.textContent = inactive;
        totalCount.textContent = csvTemplates.length;
    }
}
function deleteTemplate(templateId) {
    const index = parseInt(templateId);
    if (isNaN(index) || index < 0 || index >= csvTemplates.length) {
        showToast("Invalid template ID", "error");
        return;
    }
    const templateName = csvTemplates[index].name;
    csvTemplates.splice(index, 1);
    saveTemplates();
    updateTemplateList();
    updateTemplateButtons();
    showToast(`Template "${templateName}" deleted successfully`);
}
document.addEventListener('click', function(e) {
    if (e.target && (e.target.classList.contains('delete-template-btn') || e.target.closest('.delete-template-btn'))) {
        const templateItem = e.target.closest('.template-item');
        if (templateItem) {
            const templateId = templateItem.dataset.templateId;
            const templateName = templateItem.querySelector('.template-name').textContent;

            // Create a fresh modal each time to avoid event listener issues
            const existingModal = document.getElementById('deleteTemplateModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Create the modal
            const modal = ensureDeleteTemplateModal();

            // Set template info
            document.getElementById('templateToDeleteName').textContent = templateName;
            document.getElementById('confirmDeleteTemplate').dataset.templateId = templateId;

            // Show the modal
            const deleteModal = new bootstrap.Modal(modal);
            deleteModal.show();

            e.preventDefault();
            e.stopPropagation();
        }
    }
});
// Event listener for confirmDeleteTemplate is now handled in ensureDeleteTemplateModal
document.addEventListener('change', function(e) {
    if (e.target && e.target.classList.contains('template-status-toggle')) {
        updateTemplateCounters();
        saveTemplates();
    }
});
document.addEventListener('DOMContentLoaded', function() {
    ensureDeleteTemplateModal();
    updateTemplateCounters();

    // Add event listeners for template JSON import/export
    document.getElementById('exportAllTemplatesBtn')?.addEventListener('click', exportAllTemplatesAsJson);
    document.getElementById('importTemplateJsonBtn')?.addEventListener('click', function() {
        document.getElementById('templateJsonFileInput').click();
    });
    document.getElementById('templateJsonFileInput')?.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            importTemplateFromJson(e.target.files[0]);
            e.target.value = ''; // Clear the input
        }
    });

    // Add event listener for deleteAllTemplatesBtn
    document.getElementById('deleteAllTemplatesBtn')?.addEventListener('click', deleteAllTemplates);

    const confirmDeleteBtn = document.getElementById('confirmDeleteTemplate');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const templateId = this.dataset.templateId;
            deleteTemplate(templateId);
            const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteTemplateModal'));
            if (deleteModal) {
                deleteModal.hide();
            }
        });
    }

    // Event listener for delete-template-btn is now handled at the document level

    document.body.addEventListener('change', function(e) {
        if (e.target && e.target.classList.contains('template-status-toggle')) {
            updateTemplateCounters();
            saveTemplates();
            updateTemplateButtons();
        }
    });
});
function ensureDeleteTemplateModal() {
    if (!document.getElementById('deleteTemplateModal')) {
        const modalHTML = `
        <div class="modal fade" id="deleteTemplateModal" tabindex="-1" aria-labelledby="deleteTemplateModalLabel">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="deleteTemplateModalLabel">Confirm Delete</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete this template?</p>
                        <p class="fw-bold" id="templateToDeleteName"></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancelDeleteTemplate">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteTemplate">Delete</button>
                    </div>
                </div>
            </div>
        </div>`;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const confirmDeleteBtn = document.getElementById('confirmDeleteTemplate');
        const cancelDeleteBtn = document.getElementById('cancelDeleteTemplate');

        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', function() {
                const templateId = this.dataset.templateId;
                deleteTemplate(templateId);
                const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteTemplateModal'));
                if (deleteModal) {
                    deleteModal.hide();
                }
            }, { once: true });
        }

        if (cancelDeleteBtn) {
            cancelDeleteBtn.addEventListener('click', function() {
                const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteTemplateModal'));
                if (deleteModal) {
                    deleteModal.hide();
                }
            }, { once: true });
        }
    }
    return document.getElementById('deleteTemplateModal');
}

function loadTemplate(templateId) {
    const index = parseInt(templateId);
    if (isNaN(index) || index < 0 || index >= csvTemplates.length) {
        showToast("Invalid template ID", "error");
        return false;
    }

    const template = csvTemplates[index];
    if (!template || !template.fields) {
        showToast("Invalid template data", "error");
        return false;
    }

    // Create empty data with template fields
    const emptyData = [template.fields.reduce((acc, field) => {
        acc[field] = "";
        return acc;
    }, {})];

    // Export to CSV
    exportDataToCSV(emptyData, template.name);
    showToast(`Template "${template.name}" loaded successfully`);
    return true;
}

function saveTemplate(name, fields) {
    if (!name || !fields || !Array.isArray(fields)) {
        showToast("Invalid template data", "error");
        return false;
    }

    const template = {
        name: name,
        fields: fields,
        active: true,
        dateAdded: new Date().toISOString()
    };

    csvTemplates.push(template);
    saveTemplates();
    updateTemplateList();
    updateTemplateButtons();
    showToast(`Template "${name}" added successfully`);
    return true;
}
