// Platform Export Manager
// Handles CSV export for different stock photo platforms

function initializePlatformExport() {
    setupPlatformExportEventListeners();
}

function setupPlatformExportEventListeners() {
    // Add event listeners for platform export buttons
    const platformExportBtns = document.querySelectorAll('.platform-export-btn');
    platformExportBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const platform = e.currentTarget.getAttribute('data-platform');
            exportToPlatform(platform);
        });
    });
}

function exportToPlatform(platform) {
    if (!metadataResults || metadataResults.length === 0) {
        if (typeof showToast === 'function') {
            showToast("No data to export.", "warning");
        } else {
            alert("No data to export.");
        }
        return;
    }

    try {
        let csvData;
        let filename;
        
        switch (platform) {
            case 'shutterstock':
                csvData = generateShutterstockCSV();
                filename = 'shutterstock_export';
                break;
            case 'adobestock':
                csvData = generateAdobeStockCSV();
                filename = 'adobestock_export';
                break;
            case 'canva':
                csvData = generateCanvaCSV();
                filename = 'canva_export';
                break;
            case 'vecteezy':
                csvData = generateVecteezyCSV();
                filename = 'vecteezy_export';
                break;
            case 'freepik':
                csvData = generateFreepikCSV();
                filename = 'freepik_export';
                break;
            default:
                if (typeof showToast === 'function') {
                    showToast("Unknown platform selected.", "error");
                } else {
                    alert("Unknown platform selected.");
                }
                return;
        }

        downloadCSV(csvData, filename);
        if (typeof showToast === 'function') {
            showToast(`${platform.charAt(0).toUpperCase() + platform.slice(1)} CSV exported successfully.`, "success");
        }

    } catch (error) {
        console.error(`Error exporting ${platform} CSV:`, error);
        if (typeof showToast === 'function') {
            showToast(`Error exporting ${platform} CSV.`, "error");
        } else {
            alert(`Error exporting ${platform} CSV.`);
        }
    }
}

function generateShutterstockCSV() {
    // SS Format: Filename,Description,Keywords,Categories,Editorial,Mature content,illustration
    const headers = ['Filename', 'Description', 'Keywords', 'Categories', 'Editorial', 'Mature content', 'illustration'];
    const rows = [headers];
    
    metadataResults.forEach(item => {
        const categories = cleanCategoriesForPlatform(item.shutterstockCategories || '', 2);
        const row = [
            item.name || '',
            item.description || '',
            item.keywords || '',
            categories,
            'no', // Editorial
            'yes', // Mature content
            'no'  // illustration
        ];
        rows.push(row);
    });
    
    return Papa.unparse(rows, {
        delimiter: ",",
        header: false,
        quotes: true,
        newline: "\r\n"
    });
}

function generateAdobeStockCSV() {
    // AS Format: Filename,Title,Keywords,Category,Releases
    // Special: Use description as title for Adobe Stock
    const headers = ['Filename', 'Title', 'Keywords', 'Category', 'Releases'];
    const rows = [headers];

    metadataResults.forEach(item => {
        // Extract only the category number for Adobe Stock export
        const categoryNumber = extractAdobeStockCategoryNumber(item.adobestockCategory || '');

        const row = [
            item.name || '',
            item.description || '', // Title uses description field for Adobe Stock
            item.keywords || '',
            categoryNumber, // Category - only the number for Adobe Stock
            ''  // Releases (empty as per example)
        ];
        rows.push(row);
    });

    return Papa.unparse(rows, {
        delimiter: ",",
        header: false,
        quotes: true,
        newline: "\r\n"
    });
}

function generateCanvaCSV() {
    // Canva Format: filename,title,keywords,Artist,locale,description
    const headers = ['filename', 'title', 'keywords', 'Artist', 'locale', 'description'];
    const rows = [headers];
    
    metadataResults.forEach(item => {
        const row = [
            item.name || '',
            '', // title (empty as per example)
            item.keywords || '',
            '', // Artist (empty as per example)
            '', // locale (empty as per example)
            item.description || ''
        ];
        rows.push(row);
    });
    
    return Papa.unparse(rows, {
        delimiter: ",",
        header: false,
        quotes: true,
        newline: "\r\n"
    });
}

function generateVecteezyCSV() {
    // Vecteezy Format: Filename,Title,Description,Keywords,License
    const headers = ['Filename', 'Title', 'Description', 'Keywords', 'License'];
    const rows = [headers];
    
    metadataResults.forEach(item => {
        const row = [
            item.name || '',
            '', // Title (empty as per example)
            item.description || '',
            item.keywords || '',
            ''  // License (empty as per example)
        ];
        rows.push(row);
    });
    
    return Papa.unparse(rows, {
        delimiter: ",",
        header: false,
        quotes: true,
        newline: "\r\n"
    });
}

function generateFreepikCSV() {
    // Freepik Format: Filename,Title,Keywords,Prompt,Model
    const headers = ['Filename', 'Title', 'Keywords', 'Prompt', 'Model'];
    const rows = [headers];
    
    metadataResults.forEach(item => {
        const row = [
            item.name || '',
            '', // Title (empty as per example)
            item.keywords || '',
            '', // Prompt (empty as per example)
            ''  // Model (empty as per example)
        ];
        rows.push(row);
    });
    
    return Papa.unparse(rows, {
        delimiter: ",",
        header: false,
        quotes: true,
        newline: "\r\n"
    });
}

/**
 * Extract category number from display format
 * @param {string} categoryDisplay - The category display format
 * @returns {string} - Just the category number
 */
function extractAdobeStockCategoryNumber(categoryDisplay) {
    if (!categoryDisplay || categoryDisplay.trim() === '') {
        return '';
    }

    // Extract number from format like "1. Animals" or just "1"
    const numberMatch = categoryDisplay.trim().match(/^(\d+)/);
    return numberMatch ? numberMatch[1] : categoryDisplay.trim();
}

function cleanCategoriesForPlatform(categories, maxCategories = 2) {
    if (!categories || categories.trim() === '') {
        return '';
    }

    // Split by comma and clean up
    const categoryArray = categories.split(',')
        .map(cat => cat.trim())
        .filter(cat => cat.length > 0);

    // Check if we have more categories than allowed
    if (categoryArray.length > maxCategories) {
        console.log(`Categories field has ${categoryArray.length} categories, limiting to ${maxCategories} as per platform requirements.`);
        if (typeof showToast === 'function') {
            showToast(`Categories limited to ${maxCategories} for platform compatibility.`, "info", 3000);
        }
    }

    // Limit to maximum categories
    const limitedCategories = categoryArray.slice(0, maxCategories);

    return limitedCategories.join(',');
}

function downloadCSV(csvContent, filename) {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    link.setAttribute('download', `${filename}_${timestamp}.csv`);
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function updatePlatformExportButtonsState() {
    const platformExportBtn = document.getElementById('platformExportBtn');
    const hasData = metadataResults && metadataResults.length > 0;
    const disableExport = isProcessing || !hasData;
    
    if (platformExportBtn) {
        platformExportBtn.disabled = disableExport;
    }
}

// Global window bindings
window.initializePlatformExport = initializePlatformExport;
window.exportToPlatform = exportToPlatform;
window.updatePlatformExportButtonsState = updatePlatformExportButtonsState;
