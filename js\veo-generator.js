// Veo 2 Generator Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const veoPrompt = document.getElementById('veoPrompt');
    const veoNegativePrompt = document.getElementById('veoNegativePrompt');
    const veoModel = document.getElementById('veoModel');
    const veoCount = document.getElementById('veoCount');
    const veoDuration = document.getElementById('veoDuration');
    const veoAspectRatio = document.getElementById('veoAspectRatio');
    const veoPersonGeneration = document.getElementById('veoPersonGeneration');
    const generateVideoBtn = document.getElementById('generateVideoBtn');
    const veoResults = document.getElementById('veoResults');
    const veoGallery = document.getElementById('veoGallery');
    const veoLoading = document.getElementById('veoLoading');
    const veoProgressBar = document.getElementById('veoProgressBar');
    const veoProgressText = document.getElementById('veoProgressText');
    const downloadAllVideosBtn = document.getElementById('downloadAllVideosBtn');
    const clearVideosBtn = document.getElementById('clearVideosBtn');

    // Image upload elements
    const veoSourceImage = document.getElementById('veoSourceImage');
    const veoImagePreview = document.getElementById('veoImagePreview');
    const veoImagePlaceholder = document.getElementById('veoImagePlaceholder');
    const removeVeoImageBtn = document.getElementById('removeVeoImageBtn');
    const selectVeoImageBtn = document.getElementById('selectVeoImageBtn');
    const veoImageInput = document.getElementById('veoImageInput');

    // Check if elements exist
    if (!generateVideoBtn) {
        console.warn('Veo generator elements not found');
        return;
    }

    // Get the placeholder element
    const veoPlaceholder = document.getElementById('veoPlaceholder');

    // Handle generate button click
    if (!generateVideoBtn.hasAttribute('data-listener-added')) {
        generateVideoBtn.addEventListener('click', function() {
            // Validate prompt
            const hasPrompt = veoPrompt.value.trim() !== '';
            if (!hasPrompt) {
                showToast('Please enter a prompt for video generation', 'warning');
                return;
            }

            // Check if API key is available
            const apiKey = getActiveApiKey();
            if (!apiKey) {
                showToast('No API key found. Please add a Gemini API key in Settings', 'error');
                return;
            }

            // Show loading indicator
            veoLoading.style.display = 'block';
            veoResults.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'none';
            }

            // Reset progress
            updateProgress(0, 'Starting video generation...');

            // Prepare request parameters
            const params = {
                model: veoModel.value,
                prompt: veoPrompt.value.trim(),
                numberOfVideos: parseInt(veoCount.value),
                durationSeconds: parseInt(veoDuration.value),
                aspectRatio: veoAspectRatio.value,
                personGeneration: veoPersonGeneration.value
            };

            // Add source image if provided
            const hasImage = veoSourceImage && veoSourceImage.src && !veoSourceImage.src.endsWith('#');
            if (hasImage) {
                params.sourceImage = veoSourceImage.src;
            }

            // Add negative prompt if provided
            if (veoNegativePrompt.value.trim()) {
                params.negativePrompt = veoNegativePrompt.value.trim();
            }

            console.log("Prepared Veo parameters:", params);

            // Call the Veo API
            generateVideosWithVeo(apiKey, params)
                .then(displayGeneratedVideos)
                .catch(error => {
                    console.error('Error generating videos:', error);
                    showToast('Error generating videos: ' + error.message, 'error');
                    veoLoading.style.display = 'none';
                    if (veoPlaceholder) {
                        veoPlaceholder.style.display = 'block';
                    }
                });
        });
        generateVideoBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle download all videos button
    if (!downloadAllVideosBtn.hasAttribute('data-listener-added')) {
        downloadAllVideosBtn.addEventListener('click', function() {
            const videos = veoGallery.querySelectorAll('video');
            if (videos.length === 0) {
                showToast('No videos to download', 'warning');
                return;
            }

            // Download all videos
            downloadAllVideos(videos);
        });
        downloadAllVideosBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle clear videos button
    if (!clearVideosBtn.hasAttribute('data-listener-added')) {
        clearVideosBtn.addEventListener('click', function() {
            veoGallery.innerHTML = '';
            veoResults.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'block';
            }
        });
        clearVideosBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle image upload
    if (selectVeoImageBtn && veoImageInput) {
        if (!selectVeoImageBtn.hasAttribute('data-listener-added')) {
            selectVeoImageBtn.addEventListener('click', function() {
                veoImageInput.click();
            });
            selectVeoImageBtn.setAttribute('data-listener-added', 'true');
        }

        if (!veoImageInput.hasAttribute('data-listener-added')) {
            veoImageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        veoSourceImage.src = e.target.result;
                        veoImagePreview.classList.remove('d-none');
                        veoImagePlaceholder.classList.add('d-none');
                    };

                    reader.readAsDataURL(file);
                }
            });
            veoImageInput.setAttribute('data-listener-added', 'true');
        }
    }

    // Handle image removal
    if (removeVeoImageBtn) {
        if (!removeVeoImageBtn.hasAttribute('data-listener-added')) {
            removeVeoImageBtn.addEventListener('click', function() {
                veoSourceImage.src = '';
                veoImagePreview.classList.add('d-none');
                veoImagePlaceholder.classList.remove('d-none');
                veoImageInput.value = '';
            });
            removeVeoImageBtn.setAttribute('data-listener-added', 'true');
        }
    }

    // Function to get active API key
    function getActiveApiKey() {
        try {
            if (typeof window.getApiKey === 'function') {
                const apiKey = window.getApiKey();
                if (apiKey) {
                    return apiKey;
                }
            }
        } catch (e) {
            console.error("Error accessing window.getApiKey:", e);
        }

        // Fallback to localStorage
        const apiKey = localStorage.getItem('csvision_api_key');
        if (apiKey) {
            return apiKey;
        }

        // Try to get from apiKeys array
        const apiKeys = JSON.parse(localStorage.getItem('csvision_api_keys') || '[]');
        if (apiKeys.length > 0) {
            const activeKey = apiKeys.find(key => key.active);
            if (activeKey) {
                return activeKey.key;
            } else if (apiKeys[0]) {
                return apiKeys[0].key;
            }
        }

        return null;
    }

    // Function to update progress
    function updateProgress(percentage, text) {
        if (veoProgressBar) {
            veoProgressBar.style.width = percentage + '%';
        }
        if (veoProgressText) {
            veoProgressText.textContent = text;
        }
    }

    // Function to generate videos using Veo API
    async function generateVideosWithVeo(apiKey, params) {
        try {
            console.log("Generating videos with Veo params:", params);
            updateProgress(10, 'Preparing request...');

            const model = params.model || "veo-2.0-generate-001";

            // Use the correct Veo API endpoint
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateVideos?key=${apiKey}`;

            // Build request body according to the Veo API format
            const requestBody = {
                prompt: params.prompt,
                config: {
                    aspectRatio: params.aspectRatio,
                    personGeneration: params.personGeneration,
                    numberOfVideos: params.numberOfVideos,
                    durationSeconds: params.durationSeconds
                }
            };

            // Add image if provided
            if (params.sourceImage) {
                // Extract base64 data
                const imageData = params.sourceImage.split(',')[1];
                const mimeType = params.sourceImage.split(';')[0].split(':')[1];

                requestBody.image = {
                    imageBytes: imageData,
                    mimeType: mimeType
                };
            }

            // Add negative prompt if provided
            if (params.negativePrompt) {
                requestBody.negativePrompt = params.negativePrompt;
            }

            console.log("Sending request to Veo API:", requestBody);
            updateProgress(20, 'Sending request to Veo API...');

            // Start the video generation operation
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`Veo API error: ${response.status} - ${errorData}`);
            }

            const operationData = await response.json();
            console.log("Operation started:", operationData);

            if (!operationData.name) {
                throw new Error('No operation name returned from Veo API');
            }

            updateProgress(30, 'Video generation started...');

            // Poll for completion using the operations endpoint
            const videos = await pollForCompletion(apiKey, operationData.name);
            return videos;

        } catch (error) {
            console.error('Error in generateVideosWithVeo:', error);
            throw error;
        }
    }

    // Function to poll for operation completion
    async function pollForCompletion(apiKey, operationName) {
        const maxAttempts = 60; // 10 minutes max (10 seconds * 60)
        let attempts = 0;

        while (attempts < maxAttempts) {
            try {
                updateProgress(30 + (attempts / maxAttempts) * 60, `Generating videos... (${Math.floor(attempts * 10 / 60)}:${String(attempts * 10 % 60).padStart(2, '0')})`);

                const checkUrl = `https://generativelanguage.googleapis.com/v1beta/operations/${operationName}?key=${apiKey}`;
                const checkResponse = await fetch(checkUrl);

                if (!checkResponse.ok) {
                    throw new Error(`Operation check failed: ${checkResponse.status}`);
                }

                const operationStatus = await checkResponse.json();
                console.log("Operation status:", operationStatus);

                if (operationStatus.done) {
                    updateProgress(90, 'Processing results...');

                    if (operationStatus.error) {
                        throw new Error(`Veo generation failed: ${operationStatus.error.message}`);
                    }

                    if (operationStatus.response && operationStatus.response.generatedVideos) {
                        updateProgress(100, 'Videos generated successfully!');
                        return operationStatus.response.generatedVideos;
                    } else {
                        throw new Error('No video data in response');
                    }
                }

                // Wait 10 seconds before next check
                await new Promise(resolve => setTimeout(resolve, 10000));
                attempts++;

            } catch (error) {
                console.error('Error polling operation:', error);
                throw error;
            }
        }

        throw new Error('Video generation timed out after 10 minutes');
    }

    // Function to display generated videos
    function displayGeneratedVideos(generatedVideos) {
        try {
            console.log("Displaying videos:", generatedVideos);
            veoLoading.style.display = 'none';
            veoGallery.innerHTML = '';

            if (!generatedVideos || generatedVideos.length === 0) {
                throw new Error('No videos generated');
            }

            generatedVideos.forEach((generatedVideo, index) => {
                if (generatedVideo.video && generatedVideo.video.uri) {
                    createVideoElement(generatedVideo.video.uri, index);
                }
            });

            veoResults.style.display = 'block';
            showToast(`Successfully generated ${generatedVideos.length} video(s)!`, 'success');

        } catch (error) {
            console.error('Error displaying videos:', error);
            showToast('Error displaying videos: ' + error.message, 'error');
            veoLoading.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'block';
            }
        }
    }

    // Function to create video element
    function createVideoElement(videoUri, index) {
        const col = document.createElement('div');
        col.className = 'col-md-6 mb-2';

        const card = document.createElement('div');
        card.className = 'card veo-card';

        const video = document.createElement('video');
        video.className = 'card-img-top';
        video.style.width = '100%';
        video.style.height = 'auto';
        video.controls = true;
        video.preload = 'metadata';
        video.src = `${videoUri}&key=${getActiveApiKey()}`;

        const cardBody = document.createElement('div');
        cardBody.className = 'card-body p-2';

        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'btn btn-sm btn-primary-purple text-white bi bi-download me-1';
        downloadBtn.textContent = ' Download';
        downloadBtn.onclick = () => downloadVideo(video.src, `veo_video_${index + 1}.mp4`);

        cardBody.appendChild(downloadBtn);
        card.appendChild(video);
        card.appendChild(cardBody);
        col.appendChild(card);
        veoGallery.appendChild(col);
    }

    // Function to download video
    function downloadVideo(videoUrl, filename) {
        const a = document.createElement('a');
        a.href = videoUrl;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    // Function to download all videos
    function downloadAllVideos(videos) {
        videos.forEach((video, index) => {
            setTimeout(() => {
                downloadVideo(video.src, `veo_video_${index + 1}.mp4`);
            }, index * 1000); // Delay each download by 1 second
        });
        showToast(`Downloading ${videos.length} video(s)...`, 'info');
    }
});
