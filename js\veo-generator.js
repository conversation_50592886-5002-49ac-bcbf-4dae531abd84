// Veo 2 Generator Functionality
//
// NOTE: This is currently a mock implementation for testing the UI.
// To implement actual Veo video generation, you need to:
// 1. Set up Google Cloud Project with Vertex AI API enabled
// 2. Use the proper Vertex AI endpoint: https://us-central1-aiplatform.googleapis.com/v1/projects/{PROJECT_ID}/locations/us-central1/publishers/google/models/veo-2.0-generate-001:predict
// 3. Implement proper authentication (OAuth2 or Service Account)
// 4. Use the correct request format as shown in the Google Cloud documentation
//
// For now, this implementation shows a working UI with mock video generation.

document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const veoPrompt = document.getElementById('veoPrompt');
    const veoNegativePrompt = document.getElementById('veoNegativePrompt');
    const veoModel = document.getElementById('veoModel');
    const veoCount = document.getElementById('veoCount');
    const veoDuration = document.getElementById('veoDuration');
    const veoAspectRatio = document.getElementById('veoAspectRatio');
    const veoPersonGeneration = document.getElementById('veoPersonGeneration');
    const generateVideoBtn = document.getElementById('generateVideoBtn');
    const veoResults = document.getElementById('veoResults');
    const veoGallery = document.getElementById('veoGallery');
    const veoLoading = document.getElementById('veoLoading');
    const veoProgressBar = document.getElementById('veoProgressBar');
    const veoProgressText = document.getElementById('veoProgressText');
    const downloadAllVideosBtn = document.getElementById('downloadAllVideosBtn');
    const clearVideosBtn = document.getElementById('clearVideosBtn');

    // Image upload elements
    const veoSourceImage = document.getElementById('veoSourceImage');
    const veoImagePreview = document.getElementById('veoImagePreview');
    const veoImagePlaceholder = document.getElementById('veoImagePlaceholder');
    const removeVeoImageBtn = document.getElementById('removeVeoImageBtn');
    const selectVeoImageBtn = document.getElementById('selectVeoImageBtn');
    const veoImageInput = document.getElementById('veoImageInput');

    // Check if elements exist
    if (!generateVideoBtn) {
        console.warn('Veo generator elements not found');
        return;
    }

    // Debug: Log all important elements
    console.log('Veo elements check:');
    console.log('generateVideoBtn:', generateVideoBtn);
    console.log('veoResults:', veoResults);
    console.log('veoGallery:', veoGallery);
    console.log('veoLoading:', veoLoading);
    console.log('veoPlaceholder:', veoPlaceholder);

    // Ensure all required elements exist
    if (!veoResults || !veoGallery || !veoLoading) {
        console.error('Critical Veo elements missing!');
        console.error('veoResults:', veoResults);
        console.error('veoGallery:', veoGallery);
        console.error('veoLoading:', veoLoading);
        return;
    }

    // Get the placeholder element
    const veoPlaceholder = document.getElementById('veoPlaceholder');

    // Handle generate button click
    if (!generateVideoBtn.hasAttribute('data-listener-added')) {
        generateVideoBtn.addEventListener('click', function() {
            // Validate prompt
            const hasPrompt = veoPrompt.value.trim() !== '';
            if (!hasPrompt) {
                showToast('Please enter a prompt for video generation', 'warning');
                return;
            }

            // Check if API key is available
            const apiKey = getActiveApiKey();
            if (!apiKey) {
                showToast('No API key found. Please add a Gemini API key in Settings', 'error');
                return;
            }

            // Show loading indicator
            veoLoading.style.display = 'block';
            veoResults.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'none';
            }

            // Reset progress
            updateProgress(0, 'Starting video generation...');

            // Prepare request parameters
            const params = {
                model: veoModel.value,
                prompt: veoPrompt.value.trim(),
                numberOfVideos: parseInt(veoCount.value),
                durationSeconds: parseInt(veoDuration.value),
                aspectRatio: veoAspectRatio.value,
                personGeneration: veoPersonGeneration.value
            };

            // Add source image if provided
            const hasImage = veoSourceImage && veoSourceImage.src && !veoSourceImage.src.endsWith('#');
            if (hasImage) {
                params.sourceImage = veoSourceImage.src;
            }

            // Add negative prompt if provided
            if (veoNegativePrompt.value.trim()) {
                params.negativePrompt = veoNegativePrompt.value.trim();
            }

            console.log("Prepared Veo parameters:", params);

            // Call the Veo API
            generateVideosWithVeo(apiKey, params)
                .then(displayGeneratedVideos)
                .catch(error => {
                    console.error('Error generating videos:', error);
                    showToast('Error generating videos: ' + error.message, 'error');
                    veoLoading.style.display = 'none';
                    if (veoPlaceholder) {
                        veoPlaceholder.style.display = 'block';
                    }
                });
        });
        generateVideoBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle download all videos button
    if (!downloadAllVideosBtn.hasAttribute('data-listener-added')) {
        downloadAllVideosBtn.addEventListener('click', function() {
            const videos = veoGallery.querySelectorAll('video');
            if (videos.length === 0) {
                showToast('No videos to download', 'warning');
                return;
            }

            // Download all videos
            downloadAllVideos(videos);
        });
        downloadAllVideosBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle clear videos button
    if (!clearVideosBtn.hasAttribute('data-listener-added')) {
        clearVideosBtn.addEventListener('click', function() {
            veoGallery.innerHTML = '';
            veoResults.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'block';
            }
        });
        clearVideosBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle image upload
    if (selectVeoImageBtn && veoImageInput) {
        if (!selectVeoImageBtn.hasAttribute('data-listener-added')) {
            selectVeoImageBtn.addEventListener('click', function() {
                veoImageInput.click();
            });
            selectVeoImageBtn.setAttribute('data-listener-added', 'true');
        }

        if (!veoImageInput.hasAttribute('data-listener-added')) {
            veoImageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        veoSourceImage.src = e.target.result;
                        veoImagePreview.classList.remove('d-none');
                        veoImagePlaceholder.classList.add('d-none');
                    };

                    reader.readAsDataURL(file);
                }
            });
            veoImageInput.setAttribute('data-listener-added', 'true');
        }
    }

    // Handle image removal
    if (removeVeoImageBtn) {
        if (!removeVeoImageBtn.hasAttribute('data-listener-added')) {
            removeVeoImageBtn.addEventListener('click', function() {
                veoSourceImage.src = '';
                veoImagePreview.classList.add('d-none');
                veoImagePlaceholder.classList.remove('d-none');
                veoImageInput.value = '';
            });
            removeVeoImageBtn.setAttribute('data-listener-added', 'true');
        }
    }

    // Function to get active API key
    function getActiveApiKey() {
        try {
            if (typeof window.getApiKey === 'function') {
                const apiKey = window.getApiKey();
                if (apiKey) {
                    return apiKey;
                }
            }
        } catch (e) {
            console.error("Error accessing window.getApiKey:", e);
        }

        // Fallback to localStorage
        const apiKey = localStorage.getItem('csvision_api_key');
        if (apiKey) {
            return apiKey;
        }

        // Try to get from apiKeys array
        const apiKeys = JSON.parse(localStorage.getItem('csvision_api_keys') || '[]');
        if (apiKeys.length > 0) {
            const activeKey = apiKeys.find(key => key.active);
            if (activeKey) {
                return activeKey.key;
            } else if (apiKeys[0]) {
                return apiKeys[0].key;
            }
        }

        return null;
    }

    // Function to update progress
    function updateProgress(percentage, text) {
        if (veoProgressBar) {
            veoProgressBar.style.width = percentage + '%';
        }
        if (veoProgressText) {
            veoProgressText.textContent = text;
        }
    }

    // Function to generate videos using Veo API
    async function generateVideosWithVeo(apiKey, params) {
        try {
            console.log("Generating videos with Veo params:", params);
            updateProgress(10, 'Preparing request...');

            const model = params.model || "veo-2.0-generate-001";

            // Use the correct Vertex AI endpoint for Veo
            const projectId = 'your-project-id'; // This would need to be configured
            const location = 'us-central1';

            // For now, let's simulate video generation since the actual Veo API requires special setup
            // In a real implementation, this would use the proper Veo API endpoint
            console.log("Simulating video generation for testing purposes...");

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            updateProgress(50, 'Simulating video generation...');

            await new Promise(resolve => setTimeout(resolve, 2000));
            updateProgress(80, 'Finalizing video...');

            await new Promise(resolve => setTimeout(resolve, 1000));

            // Return mock video data for testing
            const mockVideos = [];
            for (let i = 0; i < params.numberOfVideos; i++) {
                // Use a reliable sample video URL or create a placeholder
                const mockVideoUrl = createMockVideoPlaceholder(params.aspectRatio, params.durationSeconds, i);
                mockVideos.push({
                    video: {
                        uri: mockVideoUrl
                    }
                });
            }

            updateProgress(100, 'Videos generated successfully!');
            return mockVideos;

            // TODO: Replace with actual Veo API implementation
            /*
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

            // Build request body using the generateContent format with video generation
            const requestBody = {
                contents: [{
                    parts: [{
                        text: `Generate a video: ${params.prompt}`
                    }]
                }],
                generationConfig: {
                    temperature: 0.4,
                    topP: 1,
                    topK: 32,
                    maxOutputTokens: 8192,
                    responseModalities: ["TEXT", "VIDEO"]
                },
                videoConfig: {
                    aspectRatio: params.aspectRatio,
                    personGeneration: params.personGeneration,
                    numberOfVideos: params.numberOfVideos,
                    durationSeconds: params.durationSeconds
                }
            };
            */



        } catch (error) {
            console.error('Error in generateVideosWithVeo:', error);
            throw error;
        }
    }

    // Function to create mock video placeholder for testing
    function createMockVideoPlaceholder(aspectRatio, duration, index) {
        // Create a simple data URL for a mock video poster/thumbnail
        // Since we can't easily create actual video files in browser,
        // we'll create a placeholder that shows this is a demo

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size based on aspect ratio
        if (aspectRatio === '16:9') {
            canvas.width = 640;
            canvas.height = 360;
        } else { // 9:16
            canvas.width = 360;
            canvas.height = 640;
        }

        // Create gradient background
        const colors = ['#6f42c1', '#28a745', '#dc3545', '#ffc107', '#17a2b8'];
        const color = colors[index % colors.length];

        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, color);
        gradient.addColorStop(1, '#ffffff');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add text
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 32px Arial';
        ctx.textAlign = 'center';
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;

        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        ctx.strokeText('DEMO VIDEO', centerX, centerY - 40);
        ctx.fillText('DEMO VIDEO', centerX, centerY - 40);

        ctx.font = 'bold 24px Arial';
        ctx.strokeText(`Video ${index + 1}`, centerX, centerY);
        ctx.fillText(`Video ${index + 1}`, centerX, centerY);

        ctx.font = '18px Arial';
        ctx.strokeText(`${duration}s - ${aspectRatio}`, centerX, centerY + 40);
        ctx.fillText(`${duration}s - ${aspectRatio}`, centerX, centerY + 40);

        // Convert canvas to data URL
        return canvas.toDataURL('image/png');
    }



    // Function to display generated videos
    function displayGeneratedVideos(generatedVideos) {
        try {
            console.log("Displaying videos:", generatedVideos);
            console.log("veoLoading element:", veoLoading);
            console.log("veoGallery element:", veoGallery);
            console.log("veoResults element:", veoResults);

            veoLoading.style.display = 'none';
            veoGallery.innerHTML = '';

            if (!generatedVideos || generatedVideos.length === 0) {
                throw new Error('No videos generated');
            }

            generatedVideos.forEach((generatedVideo, index) => {
                console.log(`Processing video ${index}:`, generatedVideo);
                if (generatedVideo.video && generatedVideo.video.uri) {
                    console.log(`Creating video element for URI: ${generatedVideo.video.uri}`);
                    createVideoElement(generatedVideo.video.uri, index);
                } else {
                    console.warn(`Video ${index} has no valid URI:`, generatedVideo);
                }
            });

            console.log("Setting veoResults display to block");
            veoResults.style.display = 'block';

            if (veoPlaceholder) {
                console.log("Hiding placeholder");
                veoPlaceholder.style.display = 'none';
            }

            showToast(`Successfully generated ${generatedVideos.length} video(s)!`, 'success');

        } catch (error) {
            console.error('Error displaying videos:', error);
            showToast('Error displaying videos: ' + error.message, 'error');
            veoLoading.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'block';
            }
        }
    }

    // Function to create video element
    function createVideoElement(videoUri, index) {
        console.log(`Creating video element ${index} with URI: ${videoUri}`);

        const col = document.createElement('div');
        col.className = 'col-md-6 mb-2';

        const card = document.createElement('div');
        card.className = 'card veo-card';

        // For demo purposes, create an image placeholder instead of video
        // since we're generating image data URLs, not actual videos
        const mediaElement = document.createElement('img');
        mediaElement.className = 'card-img-top';
        mediaElement.style.width = '100%';
        mediaElement.style.height = 'auto';
        mediaElement.style.borderRadius = '8px';
        mediaElement.alt = `Demo Video ${index + 1}`;

        // Set the image source
        mediaElement.src = videoUri;
        console.log(`Set image source for demo video ${index}: ${videoUri.substring(0, 50)}...`);

        // Add error handling for image loading
        mediaElement.addEventListener('load', () => {
            console.log(`Demo video image ${index} loaded successfully`);
        });

        mediaElement.addEventListener('error', (e) => {
            console.error(`Demo video image ${index} error:`, e);
            // Fallback: create a simple colored div
            mediaElement.style.display = 'none';
            const fallback = document.createElement('div');
            fallback.style.width = '100%';
            fallback.style.height = '200px';
            fallback.style.backgroundColor = '#6f42c1';
            fallback.style.display = 'flex';
            fallback.style.alignItems = 'center';
            fallback.style.justifyContent = 'center';
            fallback.style.color = 'white';
            fallback.style.fontSize = '18px';
            fallback.style.fontWeight = 'bold';
            fallback.textContent = `Demo Video ${index + 1}`;
            card.insertBefore(fallback, mediaElement);
        });

        const cardBody = document.createElement('div');
        cardBody.className = 'card-body p-2';

        // Add demo notice
        const demoNotice = document.createElement('small');
        demoNotice.className = 'text-muted d-block mb-2';
        demoNotice.innerHTML = '<i class="bi bi-info-circle"></i> Demo Mode - Placeholder Image';

        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'btn btn-sm btn-primary-purple text-white bi bi-download me-1';
        downloadBtn.textContent = ' Download Demo';
        downloadBtn.onclick = () => {
            // For demo, just show a message
            showToast('Demo mode - No actual video to download', 'info');
        };

        cardBody.appendChild(demoNotice);
        cardBody.appendChild(downloadBtn);
        card.appendChild(mediaElement);
        card.appendChild(cardBody);
        col.appendChild(card);

        console.log(`Appending demo video ${index} to gallery`);
        veoGallery.appendChild(col);

        console.log(`Demo video element ${index} created and added to DOM`);
    }

    // Function to download video
    function downloadVideo(videoUrl, filename) {
        const a = document.createElement('a');
        a.href = videoUrl;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    // Function to download all videos
    function downloadAllVideos(videos) {
        videos.forEach((video, index) => {
            setTimeout(() => {
                downloadVideo(video.src, `veo_video_${index + 1}.mp4`);
            }, index * 1000); // Delay each download by 1 second
        });
        showToast(`Downloading ${videos.length} video(s)...`, 'info');
    }
});
