/* Content Sub-tabs Styling */
.content-subtabs {
    margin-bottom: 1.5rem;
}

.content-subtabs .nav-pills {
    background-color: transparent;
    border-radius: 0;
    padding: 0;
}

.content-subtab {
    cursor: pointer;
    padding: 8px 16px;
    margin-right: 4px;
    border-radius: 4px 4px 0 0;
    background-color: var(--light-purple);
    color: var(--text-on-light);
    border: 1px solid var(--medium-purple);
    border-bottom: none;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.content-subtab:hover {
    background-color: var(--medium-purple);
    color: var(--dark-purple);
}

.content-subtab.active,
.nav-link.content-subtab.active {
    background-color: var(--primary-purple) !important;
    color: white !important;
    border-color: var(--primary-purple) !important;
}

/* Content Sub-tab Content Styling */
.content-subcontent {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.content-subcontent.active {
    display: block;
    opacity: 1;
}

/* Animation for tab transitions */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-subcontent.active {
    animation: fadeIn 0.3s ease forwards;
}

/* Imagen tab styling */
.alert-light-purple {
    background-color: var(--light-purple);
    border-color: var(--medium-purple);
    color: var(--dark-purple);
}

.btn-primary-purple {
    background-color: var(--primary-purple);
    border-color: var(--primary-purple);
}

.btn-primary-purple:hover {
    background-color: var(--hard-purple);
    border-color: var(--hard-purple);
}

.text-primary-purple {
    color: var(--primary-purple);
}

.bg-primary-purple {
    background-color: var(--primary-purple);
}

/* Image gallery styling */
#imagenGallery .card {
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
    margin-bottom: 10px;
    height: auto !important;
    display: flex;
    flex-direction: column;
}

#imagenGallery .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-purple);
}

#imagenGallery .card-img-top {
    width: 100%;
    object-fit: contain;
    cursor: pointer;
}

#imagenGallery .position-relative {
    flex-grow: 1;
}

#imagenGallery .card-body, .imagen-card-body {
    padding: 0.5rem;
    overflow: hidden;
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: 0 0 auto !important;
}

/* Specific styling for veo tab */
#veo-subtab .card-header {
    background-color: var(--primary-purple);
    color: white;
}

#veo-subtab .form-control:focus,
#veo-subtab .form-select:focus {
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}

#veo-subtab .form-label {
    font-weight: 500;
    color: var(--dark-purple);
}

.veo-card-body {
    padding: 0.5rem;
    overflow: hidden;
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: 0 0 auto !important;
}

/* Specific styling for imagen tab */
#imagen-subtab .card-header {
    background-color: var(--primary-purple);
    color: white;
}

#imagen-subtab .form-control:focus,
#imagen-subtab .form-select:focus {
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}

#imagen-subtab .form-label {
    font-weight: 500;
    color: var(--dark-purple);
}

/* Make sure content doesn't overflow */
#myTabContent {
    overflow: hidden;
}

/* Scrollbar styling for imagen results */
.imagen-results-container {
    scrollbar-width: thin;
    scrollbar-color: var(--medium-purple) #f0f0f0;
}

.imagen-results-container::-webkit-scrollbar {
    width: 8px;
}

.imagen-results-container::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px;
}

.imagen-results-container::-webkit-scrollbar-thumb {
    background-color: var(--medium-purple);
    border-radius: 10px;
}

/* Fullscreen modal styling */
#imagenFullscreenModal .modal-content {
    background-color: #f8f9fa;
    border: none;
}

#imagenFullscreenModal .modal-header {
    border-bottom-color: var(--medium-purple);
}

#imagenFullscreenModal .modal-footer {
    border-top-color: var(--medium-purple);
}

#imagen-card {
    height: calc(100vh - 202px) !important;
    overflow: hidden;
}

#prompter-card {
    height: calc(100vh - 209px) !important;
    overflow: hidden;
}

#imagen-content #imagen-card-body-control{
    overflow-y: auto;
    height: calc(100vh - 280px) !important;
    max-height: 100%;
}

#prompter-card-body-control {
    overflow-y: auto;
    height: calc(100vh - 255px) !important;
    max-height: 100%;
}

/* Ensure inner cards don't overflow */
#prompter-content .col-md-8 .card,
#prompter-content .col-md-4 .card{
    height: calc(100vh - 230px) !important;
    overflow: hidden;
}

#imagen-content .col-md-9 .card{
    height: fit-content !important;
    overflow: hidden;
}

#imagen-content .col-md-3 .card {
    height: calc(100vh - 230px) !important;
    overflow: hidden;
}

/* Prompter table styling */
.prompter-table-container {
    height: calc(100vh - 250px) !important;
    max-height: 100%;
}

.prompter-table-container .tabulator {
    height: 100% !important;
}

.prompter-table-container .tabulator .tabulator-tableHolder {
    overflow-y: auto;
    height: calc(100% - 70px) !important; /* Adjust for header and footer */
}

.prompter-table-container .tabulator-footer {
    background-color: var(--primary-purple);
    border-top: 1px solid var(--hard-purple);
    padding: 5px 10px;
    color: var(--text-on-purple);
}

/* Prompter loading overlay styling */
.prompter-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(138, 109, 193, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: 4px;
}

.prompter-loading-content {
    text-align: center;
    padding: 30px;
    background-color: transparent;
    border-radius: 8px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.prompter-loading-content .spinner-border {
    width: 4rem;
    height: 4rem;
    border-width: 0.3em;
}

.prompter-loading-content p {
    font-size: 1.2rem;
    margin-top: 20px;
    font-weight: 500;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}